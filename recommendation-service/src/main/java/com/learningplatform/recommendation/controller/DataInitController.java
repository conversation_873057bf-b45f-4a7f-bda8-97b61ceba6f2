package com.learningplatform.recommendation.controller;

import com.learningplatform.recommendation.model.UserBehaviorModel;
import com.learningplatform.recommendation.repository.UserBehaviorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 推荐服务数据初始化控制器
 */
@RestController
@RequestMapping("/api/recommendation/init")
public class DataInitController {
    
    @Autowired
    private UserBehaviorRepository userBehaviorRepository;
    
    @PostMapping("/test-data")
    public Map<String, Object> initTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 清除现有数据
            userBehaviorRepository.findAll().clear();
            
            // 用户1的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(1L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 1L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(1L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户2的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 1L, "COMPLETE", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(2L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户3的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(3L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(3L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户4的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(4L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 2L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(4L, 5L, "COMPLETE", BigDecimal.valueOf(1.0)));
            
            // 用户5的行为数据
            userBehaviorRepository.save(new UserBehaviorModel(5L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(5L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            
            // 用户6的行为数据（当前登录用户）
            userBehaviorRepository.save(new UserBehaviorModel(6L, 1L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 2L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 3L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 3L, "ENROLL", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 4L, "VIEW", BigDecimal.valueOf(1.0)));
            userBehaviorRepository.save(new UserBehaviorModel(6L, 4L, "ENROLL", BigDecimal.valueOf(1.0)));
            
            result.put("success", true);
            result.put("message", "推荐服务测试数据初始化成功");
            result.put("totalBehaviors", userBehaviorRepository.findAll().size());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}
