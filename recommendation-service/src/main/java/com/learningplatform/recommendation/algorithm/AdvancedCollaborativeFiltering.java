package com.learningplatform.recommendation.algorithm;

import com.learningplatform.recommendation.model.UserBehaviorModel;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 高级协同过滤算法实现
 * 包含基于用户的协同过滤、基于物品的协同过滤和矩阵分解
 */
public class AdvancedCollaborativeFiltering {
    
    private final Map<String, Double> behaviorWeights;
    private final double minSimilarity;
    private final int minCommonItems;
    
    public AdvancedCollaborativeFiltering() {
        this.behaviorWeights = Map.of(
            "VIEW", 0.1,
            "ENROLL", 0.5,
            "COMPLETE", 1.0,
            "RATE", 0.8,
            "FEEDBACK", 0.3
        );
        this.minSimilarity = 0.1;
        this.minCommonItems = 2;
    }
    
    /**
     * 基于用户的协同过滤推荐
     */
    public List<CourseRecommendation> userBasedCollaborativeFiltering(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 构建用户-课程评分矩阵
        Map<Long, Map<Long, Double>> userCourseMatrix = buildUserCourseMatrix(allBehaviors);
        
        // 获取目标用户的评分
        Map<Long, Double> targetUserRatings = userCourseMatrix.get(targetUserId);
        if (targetUserRatings == null || targetUserRatings.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 计算用户相似度
        Map<Long, Double> userSimilarities = calculateUserSimilarities(targetUserId, userCourseMatrix);
        
        // 基于相似用户进行推荐
        Map<Long, Double> courseScores = new HashMap<>();
        Set<Long> targetUserCourses = targetUserRatings.keySet();
        
        for (Map.Entry<Long, Double> simEntry : userSimilarities.entrySet()) {
            Long similarUserId = simEntry.getKey();
            Double similarity = simEntry.getValue();
            
            if (similarity < minSimilarity) continue;
            
            Map<Long, Double> similarUserRatings = userCourseMatrix.get(similarUserId);
            if (similarUserRatings == null) continue;
            
            for (Map.Entry<Long, Double> courseEntry : similarUserRatings.entrySet()) {
                Long courseId = courseEntry.getKey();
                Double rating = courseEntry.getValue();
                
                // 只推荐目标用户未学习的课程
                if (!targetUserCourses.contains(courseId)) {
                    // 使用加权平均计算推荐分数
                    double weightedScore = similarity * rating;
                    courseScores.merge(courseId, weightedScore, Double::sum);
                }
            }
        }
        
        // 归一化分数并排序
        return courseScores.entrySet().stream()
                .map(entry -> new CourseRecommendation(
                    entry.getKey(), 
                    normalizeScore(entry.getValue()),
                    "基于用户协同过滤推荐"
                ))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    /**
     * 基于物品的协同过滤推荐
     */
    public List<CourseRecommendation> itemBasedCollaborativeFiltering(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 构建课程-用户评分矩阵（转置矩阵）
        Map<Long, Map<Long, Double>> courseUserMatrix = buildCourseUserMatrix(allBehaviors);
        
        // 获取目标用户学习过的课程
        Set<Long> userCourses = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(targetUserId))
                .map(UserBehaviorModel::getCourseId)
                .collect(Collectors.toSet());
        
        if (userCourses.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 计算课程相似度
        Map<Long, Map<Long, Double>> courseSimilarities = calculateCourseSimilarities(courseUserMatrix);
        
        // 基于相似课程进行推荐
        Map<Long, Double> courseScores = new HashMap<>();
        
        for (Long userCourse : userCourses) {
            Map<Long, Double> similarCourses = courseSimilarities.get(userCourse);
            if (similarCourses == null) continue;
            
            // 获取用户对该课程的评分
            double userRating = getUserCourseRating(targetUserId, userCourse, allBehaviors);
            
            for (Map.Entry<Long, Double> simEntry : similarCourses.entrySet()) {
                Long similarCourseId = simEntry.getKey();
                Double similarity = simEntry.getValue();
                
                if (similarity < minSimilarity || userCourses.contains(similarCourseId)) {
                    continue;
                }
                
                double weightedScore = similarity * userRating;
                courseScores.merge(similarCourseId, weightedScore, Double::sum);
            }
        }
        
        // 归一化分数并排序
        return courseScores.entrySet().stream()
                .map(entry -> new CourseRecommendation(
                    entry.getKey(), 
                    normalizeScore(entry.getValue()),
                    "基于物品协同过滤推荐"
                ))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    /**
     * 矩阵分解推荐
     */
    public List<CourseRecommendation> matrixFactorizationRecommendation(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 构建用户-课程评分矩阵
        Map<Long, Map<Long, Double>> userCourseMatrix = buildUserCourseMatrix(allBehaviors);
        
        // 创建并训练矩阵分解模型
        MatrixFactorization mf = new MatrixFactorization(10, 0.01, 0.01, 100);
        mf.train(userCourseMatrix);
        
        // 获取用户已学习的课程
        Set<Long> userCourses = allBehaviors.stream()
                .filter(b -> b.getUserId().equals(targetUserId))
                .map(UserBehaviorModel::getCourseId)
                .collect(Collectors.toSet());
        
        // 使用矩阵分解模型进行推荐
        List<Long> recommendedCourses = mf.recommend(targetUserId, userCourses, topN);
        
        return recommendedCourses.stream()
                .map(courseId -> {
                    double score = mf.predict(targetUserId, courseId);
                    return new CourseRecommendation(courseId, score, "基于矩阵分解推荐");
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 混合协同过滤推荐（结合多种方法）
     */
    public List<CourseRecommendation> hybridCollaborativeFiltering(
            Long targetUserId, 
            List<UserBehaviorModel> allBehaviors, 
            int topN) {
        
        // 获取不同方法的推荐结果
        List<CourseRecommendation> userBasedRecs = userBasedCollaborativeFiltering(targetUserId, allBehaviors, topN * 2);
        List<CourseRecommendation> itemBasedRecs = itemBasedCollaborativeFiltering(targetUserId, allBehaviors, topN * 2);
        List<CourseRecommendation> matrixFactRecs = matrixFactorizationRecommendation(targetUserId, allBehaviors, topN * 2);
        
        // 合并推荐结果
        Map<Long, Double> hybridScores = new HashMap<>();
        
        // 用户协同过滤权重 0.4
        for (CourseRecommendation rec : userBasedRecs) {
            hybridScores.merge(rec.courseId, rec.score * 0.4, Double::sum);
        }
        
        // 物品协同过滤权重 0.3
        for (CourseRecommendation rec : itemBasedRecs) {
            hybridScores.merge(rec.courseId, rec.score * 0.3, Double::sum);
        }
        
        // 矩阵分解权重 0.3
        for (CourseRecommendation rec : matrixFactRecs) {
            hybridScores.merge(rec.courseId, rec.score * 0.3, Double::sum);
        }
        
        // 生成最终推荐结果
        return hybridScores.entrySet().stream()
                .map(entry -> new CourseRecommendation(
                    entry.getKey(), 
                    entry.getValue(),
                    "基于混合协同过滤推荐"
                ))
                .sorted((a, b) -> Double.compare(b.score, a.score))
                .limit(topN)
                .collect(Collectors.toList());
    }
    
    private Map<Long, Map<Long, Double>> buildUserCourseMatrix(List<UserBehaviorModel> behaviors) {
        Map<Long, Map<Long, Double>> matrix = new HashMap<>();
        
        for (UserBehaviorModel behavior : behaviors) {
            Long userId = behavior.getUserId();
            Long courseId = behavior.getCourseId();
            
            Double weight = behaviorWeights.getOrDefault(behavior.getBehaviorType(), 0.1);
            Double behaviorValue = behavior.getBehaviorValue() != null ? 
                behavior.getBehaviorValue().doubleValue() : 1.0;
            Double score = weight * behaviorValue;
            
            matrix.computeIfAbsent(userId, k -> new HashMap<>())
                  .merge(courseId, score, Double::sum);
        }
        
        return matrix;
    }
    
    private Map<Long, Map<Long, Double>> buildCourseUserMatrix(List<UserBehaviorModel> behaviors) {
        Map<Long, Map<Long, Double>> matrix = new HashMap<>();
        
        for (UserBehaviorModel behavior : behaviors) {
            Long userId = behavior.getUserId();
            Long courseId = behavior.getCourseId();
            
            Double weight = behaviorWeights.getOrDefault(behavior.getBehaviorType(), 0.1);
            Double behaviorValue = behavior.getBehaviorValue() != null ? 
                behavior.getBehaviorValue().doubleValue() : 1.0;
            Double score = weight * behaviorValue;
            
            matrix.computeIfAbsent(courseId, k -> new HashMap<>())
                  .merge(userId, score, Double::sum);
        }
        
        return matrix;
    }
    
    private Map<Long, Double> calculateUserSimilarities(Long targetUserId, Map<Long, Map<Long, Double>> userCourseMatrix) {
        Map<Long, Double> similarities = new HashMap<>();
        Map<Long, Double> targetUserRatings = userCourseMatrix.get(targetUserId);
        
        if (targetUserRatings == null || targetUserRatings.isEmpty()) {
            return similarities;
        }
        
        for (Map.Entry<Long, Map<Long, Double>> entry : userCourseMatrix.entrySet()) {
            Long userId = entry.getKey();
            Map<Long, Double> userRatings = entry.getValue();
            
            if (!userId.equals(targetUserId)) {
                double similarity = calculatePearsonCorrelation(targetUserRatings, userRatings);
                if (similarity > minSimilarity) {
                    similarities.put(userId, similarity);
                }
            }
        }
        
        return similarities;
    }
    
    private Map<Long, Map<Long, Double>> calculateCourseSimilarities(Map<Long, Map<Long, Double>> courseUserMatrix) {
        Map<Long, Map<Long, Double>> similarities = new HashMap<>();
        
        List<Long> courses = new ArrayList<>(courseUserMatrix.keySet());
        
        for (int i = 0; i < courses.size(); i++) {
            Long courseA = courses.get(i);
            Map<Long, Double> ratingsA = courseUserMatrix.get(courseA);
            
            for (int j = i + 1; j < courses.size(); j++) {
                Long courseB = courses.get(j);
                Map<Long, Double> ratingsB = courseUserMatrix.get(courseB);
                
                double similarity = calculatePearsonCorrelation(ratingsA, ratingsB);
                if (similarity > minSimilarity) {
                    similarities.computeIfAbsent(courseA, k -> new HashMap<>()).put(courseB, similarity);
                    similarities.computeIfAbsent(courseB, k -> new HashMap<>()).put(courseA, similarity);
                }
            }
        }
        
        return similarities;
    }
    
    private double calculatePearsonCorrelation(Map<Long, Double> ratingsA, Map<Long, Double> ratingsB) {
        Set<Long> commonItems = new HashSet<>(ratingsA.keySet());
        commonItems.retainAll(ratingsB.keySet());
        
        if (commonItems.size() < minCommonItems) {
            return 0.0;
        }
        
        double sumA = 0.0, sumB = 0.0;
        double sumA2 = 0.0, sumB2 = 0.0;
        double sumAB = 0.0;
        
        for (Long item : commonItems) {
            double ratingA = ratingsA.get(item);
            double ratingB = ratingsB.get(item);
            
            sumA += ratingA;
            sumB += ratingB;
            sumA2 += ratingA * ratingA;
            sumB2 += ratingB * ratingB;
            sumAB += ratingA * ratingB;
        }
        
        int n = commonItems.size();
        double numerator = sumAB - (sumA * sumB / n);
        double denominator = Math.sqrt((sumA2 - sumA * sumA / n) * (sumB2 - sumB * sumB / n));
        
        if (denominator == 0.0) {
            return 0.0;
        }
        
        return numerator / denominator;
    }
    
    private double getUserCourseRating(Long userId, Long courseId, List<UserBehaviorModel> behaviors) {
        return behaviors.stream()
                .filter(b -> b.getUserId().equals(userId) && b.getCourseId().equals(courseId))
                .mapToDouble(b -> {
                    Double weight = behaviorWeights.getOrDefault(b.getBehaviorType(), 0.1);
                    Double behaviorValue = b.getBehaviorValue() != null ? 
                        b.getBehaviorValue().doubleValue() : 1.0;
                    return weight * behaviorValue;
                })
                .sum();
    }
    
    private double normalizeScore(double score) {
        // 使用sigmoid函数进行归一化
        return 1.0 / (1.0 + Math.exp(-score));
    }
    
    public static class CourseRecommendation {
        public final Long courseId;
        public final double score;
        public final String reason;
        
        public CourseRecommendation(Long courseId, double score, String reason) {
            this.courseId = courseId;
            this.score = score;
            this.reason = reason;
        }
    }
}