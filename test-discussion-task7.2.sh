#!/bin/bash

echo "=== Task 7.2 讨论功能实现 完整测试 ==="

# 1. 健康检查
echo "1. 健康检查:"
curl -s http://localhost:8085/health | jq .
echo -e "\n"

# 2. 初始化测试数据
echo "2. 初始化测试数据:"
curl -s -X POST http://localhost:8085/api/init/test-data | jq .
echo -e "\n"

# 3. 创建第一个讨论主题
echo "3. 创建第一个讨论主题:"
TOPIC1_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/topics \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 1" \
  -d '{
    "courseId": 1,
    "title": "Spring Boot学习讨论",
    "content": "大家在学习Spring Boot过程中遇到了什么问题？"
  }')
echo $TOPIC1_RESPONSE | jq .
TOPIC1_ID=$(echo $TOPIC1_RESPONSE | jq -r '.data.id // empty')
echo -e "\n"

# 4. 创建第二个讨论主题
echo "4. 创建第二个讨论主题:"
TOPIC2_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/topics \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 1" \
  -d '{
    "courseId": 1,
    "title": "MyBatis Plus使用技巧",
    "content": "分享一些MyBatis Plus的使用经验和技巧"
  }')
echo $TOPIC2_RESPONSE | jq .
TOPIC2_ID=$(echo $TOPIC2_RESPONSE | jq -r '.data.id // empty')
echo -e "\n"

if [ ! -z "$TOPIC1_ID" ] && [ ! -z "$TOPIC2_ID" ]; then
    # 5. 测试主题列表查询（默认排序）
    echo "5. 获取课程主题列表（默认排序）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10" | jq .
    echo -e "\n"

    # 6. 测试主题列表查询（按热度排序）
    echo "6. 获取课程主题列表（按热度排序）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10&sortBy=hot" | jq .
    echo -e "\n"

    # 7. 测试主题列表查询（按回复数排序）
    echo "7. 获取课程主题列表（按回复数排序）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10&sortBy=replies" | jq .
    echo -e "\n"

    # 8. 测试主题列表查询（按查看数排序）
    echo "8. 获取课程主题列表（按查看数排序）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10&sortBy=views" | jq .
    echo -e "\n"

    # 9. 获取热门主题列表
    echo "9. 获取热门主题列表:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics/hot?page=1&size=10" | jq .
    echo -e "\n"

    # 10. 搜索主题功能
    echo "10. 搜索主题功能:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics/search?keyword=Spring&page=1&size=10" | jq .
    echo -e "\n"

    # 11. 获取主题详情（会增加查看次数）
    echo "11. 获取主题详情:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC1_ID" | jq .
    echo -e "\n"

    # 12. 创建回复
    echo "12. 创建回复:"
    REPLY1_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/replies \
      -H "Content-Type: application/json" \
      -H "X-User-Id: 1" \
      -d "{
        \"topicId\": $TOPIC1_ID,
        \"content\": \"我在配置数据库连接时遇到了问题\"
      }")
    echo $REPLY1_RESPONSE | jq .
    REPLY1_ID=$(echo $REPLY1_RESPONSE | jq -r '.data.id // empty')
    echo -e "\n"

    # 13. 创建嵌套回复
    if [ ! -z "$REPLY1_ID" ]; then
        echo "13. 创建嵌套回复:"
        REPLY2_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/replies \
          -H "Content-Type: application/json" \
          -H "X-User-Id: 1" \
          -d "{
            \"topicId\": $TOPIC1_ID,
            \"parentId\": $REPLY1_ID,
            \"content\": \"你可以检查一下application.yml中的数据库配置\"
          }")
        echo $REPLY2_RESPONSE | jq .
        REPLY2_ID=$(echo $REPLY2_RESPONSE | jq -r '.data.id // empty')
        echo -e "\n"

        # 14. 点赞回复功能
        echo "14. 点赞回复功能:"
        curl -s -X POST "http://localhost:8085/api/discussion/replies/$REPLY1_ID/like" \
          -H "X-User-Id: 1" | jq .
        echo -e "\n"

        # 15. 测试重复点赞（应该失败）
        echo "15. 测试重复点赞（应该失败）:"
        curl -s -X POST "http://localhost:8085/api/discussion/replies/$REPLY1_ID/like" \
          -H "X-User-Id: 1" | jq .
        echo -e "\n"

        # 16. 取消点赞功能
        echo "16. 取消点赞功能:"
        curl -s -X DELETE "http://localhost:8085/api/discussion/replies/$REPLY1_ID/like" \
          -H "X-User-Id: 1" | jq .
        echo -e "\n"
    fi

    # 17. 获取主题回复列表（分页）
    echo "17. 获取主题回复列表（分页）:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC1_ID/replies?page=1&size=10" | jq .
    echo -e "\n"

    # 18. 获取主题所有回复（嵌套结构）
    echo "18. 获取主题所有回复（嵌套结构）:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC1_ID/replies/all" | jq .
    echo -e "\n"

    # 19. 置顶主题功能
    echo "19. 置顶主题功能:"
    curl -s -X POST "http://localhost:8085/api/discussion/topics/$TOPIC1_ID/pin" \
      -H "X-User-Id: 1" | jq .
    echo -e "\n"

    # 20. 验证置顶效果（置顶的主题应该排在前面）
    echo "20. 验证置顶效果:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10" | jq .
    echo -e "\n"

    # 21. 取消置顶功能
    echo "21. 取消置顶功能:"
    curl -s -X DELETE "http://localhost:8085/api/discussion/topics/$TOPIC1_ID/pin" \
      -H "X-User-Id: 1" | jq .
    echo -e "\n"

    # 22. 删除回复功能
    if [ ! -z "$REPLY2_ID" ]; then
        echo "22. 删除回复功能:"
        curl -s -X DELETE "http://localhost:8085/api/discussion/replies/$REPLY2_ID" \
          -H "X-User-Id: 1" | jq .
        echo -e "\n"
    fi

    # 23. 删除主题功能
    echo "23. 删除主题功能:"
    curl -s -X DELETE "http://localhost:8085/api/discussion/topics/$TOPIC2_ID" \
      -H "X-User-Id: 1" | jq .
    echo -e "\n"

    # 24. 验证删除效果
    echo "24. 验证删除效果（主题列表应该只剩一个）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=10" | jq .
    echo -e "\n"

else
    echo "主题创建失败，跳过后续测试"
fi

echo "=== Task 7.2 讨论功能实现测试完成 ==="