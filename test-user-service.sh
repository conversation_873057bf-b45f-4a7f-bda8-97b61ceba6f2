#!/bin/bash

echo "Testing User Service APIs..."

# Test user registration
echo "1. Testing user registration..."
curl -X POST http://localhost:8081/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "Test User"
  }' | jq .

echo -e "\n"

# Test user login
echo "2. Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8081/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "testuser",
    "password": "password123"
  }')

echo $LOGIN_RESPONSE | jq .

# Extract token for further tests
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token')
echo "Token: $TOKEN"

echo -e "\n"

# Test get user profile
echo "3. Testing get user profile..."
curl -X GET http://localhost:8081/api/user/profile \
  -H "Authorization: Bearer $TOKEN" | jq .

echo -e "\n"

# Test update user profile
echo "4. Testing update user profile..."
curl -X PUT http://localhost:8081/api/user/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "nickname": "Updated Test User",
    "phone": "1234567890"
  }' | jq .

echo -e "\n"
echo "User Service API tests completed!"