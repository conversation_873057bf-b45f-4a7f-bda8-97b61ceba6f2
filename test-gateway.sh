#!/bin/bash

echo "Testing Gateway Service..."

# Test health endpoint
echo "1. Testing health endpoint:"
curl -s http://localhost:8080/actuator/health | jq . || echo "Health endpoint not accessible"

# Test gateway routes endpoint
echo -e "\n2. Testing gateway routes:"
curl -s http://localhost:8080/actuator/gateway/routes | jq . || echo "Routes endpoint not accessible"

# Test CORS preflight request
echo -e "\n3. Testing CORS preflight:"
curl -s -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  http://localhost:8080/api/user/login -v

# Test JWT authentication (should fail without token)
echo -e "\n4. Testing JWT authentication (should return 401):"
curl -s -X GET http://localhost:8080/api/user/profile | jq . || echo "No JSON response"

# Test excluded path (should work without token)
echo -e "\n5. Testing excluded path (should work):"
curl -s -X GET http://localhost:8080/api/user/login | jq . || echo "No JSON response"

echo -e "\nGateway tests completed."