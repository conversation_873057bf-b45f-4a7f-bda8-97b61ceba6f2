#!/bin/bash

echo "=== 讨论服务简单API测试 ==="

# 1. 健康检查
echo "1. 健康检查:"
curl -s http://localhost:8085/health | jq .
echo -e "\n"

# 2. 测试获取课程讨论主题列表（即使为空也应该返回成功）
echo "2. 获取课程讨论主题列表（课程ID 1）:"
curl -s "http://localhost:8085/api/discussion/topics/course/1?page=1&size=10" | jq .
echo -e "\n"

# 3. 测试获取不存在主题的回复列表
echo "3. 获取主题回复列表（主题ID 999）:"
curl -s "http://localhost:8085/api/discussion/replies/topic/999?page=1&size=10" | jq .
echo -e "\n"

echo "=== 基础API测试完成 ==="