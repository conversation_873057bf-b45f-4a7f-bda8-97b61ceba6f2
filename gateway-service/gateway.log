[2m2025-08-01T18:35:52.406+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:35:52.467+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:35:52.483+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.l.gateway.GatewayApplication          [0;39m [2m:[0;39m Starting GatewayApplication using Java 23.0.2 with PID 29796 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/gateway-service/target/gateway-service-1.0.0.jar started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/gateway-service)
[2m2025-08-01T18:35:52.483+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.l.gateway.GatewayApplication          [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:35:52.483+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.l.gateway.GatewayApplication          [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:35:53.144+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=a34d323f-227d-3c0d-bae3-17e9a9b84f00
[2m2025-08-01T18:35:53.177+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[2m2025-08-01T18:35:53.178+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration' of type [org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.179+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration' of type [org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.183+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.184+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.184+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x000001e0013bef50] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.184+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.188+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.190+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.190+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.190+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerClientFactory' of type [org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.192+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'blockingLoadBalancerClient' of type [org.springframework.cloud.loadbalancer.blocking.client.BlockingLoadBalancerClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.196+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerServiceInstanceCookieTransformer' of type [org.springframework.cloud.loadbalancer.core.LoadBalancerServiceInstanceCookieTransformer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.197+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blocking.XForwardedHeadersTransformer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.197+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.198+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerRequestFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.198+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.198+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[2m2025-08-01T18:35:53.199+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.199+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:35:53.293+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8090 (http)
[2m2025-08-01T18:35:53.298+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:35:53.298+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:35:53.561+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.gateway.config.GatewayProperties  [0;39m [2m:[0;39m Routes supplied from Gateway Properties: [RouteDefinition{id='user-service', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/api/user/**}}], filters=[], uri=lb://user-service, order=0, metadata={}}, RouteDefinition{id='course-service', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/api/course/**}}], filters=[], uri=lb://course-service, order=0, metadata={}}, RouteDefinition{id='learning-service', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/api/learning/**}}], filters=[], uri=lb://learning-service, order=0, metadata={}}, RouteDefinition{id='recommendation-service', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/api/recommendation/**}}], filters=[], uri=lb://recommendation-service, order=0, metadata={}}, RouteDefinition{id='discussion-service', predicates=[PredicateDefinition{name='Path', args={_genkey_0=/api/discussion/**}}], filters=[], uri=lb://discussion-service, order=0, metadata={}}]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [After]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Before]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Between]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Cookie]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Header]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Host]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Method]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Path]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Query]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [ReadBody]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [RemoteAddr]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [Weight]
[2m2025-08-01T18:35:53.642+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m Loaded RoutePredicateFactory [CloudFoundryRouteService]
[2m2025-08-01T18:35:53.726+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 3 endpoint(s) beneath base path '/actuator'
[2m2025-08-01T18:35:53.819+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36miguration$LoadBalancerCaffeineWarnLogger[0;39m [2m:[0;39m Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[2m2025-08-01T18:35:53.853+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8090 (http) with context path ''
[2m2025-08-01T18:35:53.859+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:35:53.859+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:35:53.859+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:35:53.860+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:35:53.869+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:35:53.869+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:35:53.870+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:35:53.872+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:35:53.872+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:35:53.887+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:35:53.891+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 2dd423aa-e75b-463b-a88c-903252d08782
[2m2025-08-01T18:35:53.899+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:35:53.899+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:35:53.900+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:35:53.901+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:35:53.920+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:35:54.148+08:00[0;39m [31mERROR[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754044554103_127.0.0.1_51169]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:35:54.247+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Success to connect to server [localhost:8848] on start up, connectionId = 1754044554103_127.0.0.1_51169
[2m2025-08-01T18:35:54.248+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Notify connected event to listeners.
[2m2025-08-01T18:35:54.248+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:35:54.248+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:35:54.248+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x000001e00176b140
[2m2025-08-01T18:35:54.249+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service gateway-service with instance Instance{instanceId='null', ip='*************', port=8090, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:35:54.259+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP gateway-service *************:8090 register finished
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:user-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:course-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-5][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:learning-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:user-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:course-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-10][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:discussion-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-6][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:learning-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:discussion-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:recommendation-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:recommendation-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:gateway-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.287+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:gateway-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T18:35:54.298+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(0) service: DEFAULT_GROUP@@gateway-service -> []
[2m2025-08-01T18:35:54.299+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-10][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m out of date data received, old-t: 1754044554292, new-t: 1754044554291
[2m2025-08-01T18:35:54.299+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m out of date data received, old-t: 1754044554292, new-t: 1754044554291
[2m2025-08-01T18:35:54.300+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.300+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@discussion-service -> [{"instanceId":"*************#8085#DEFAULT#DEFAULT_GROUP@@discussion-service","ip":"*************","port":8085,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@discussion-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.300+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@user-service -> [{"instanceId":"*************#8081#DEFAULT#DEFAULT_GROUP@@user-service","ip":"*************","port":8081,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@user-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.300+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@recommendation-service -> [{"instanceId":"*************#8084#DEFAULT#DEFAULT_GROUP@@recommendation-service","ip":"*************","port":8084,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@recommendation-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.300+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-6][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@learning-service -> [{"instanceId":"*************#8083#DEFAULT#DEFAULT_GROUP@@learning-service","ip":"*************","port":8083,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@learning-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.302+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@discussion-service -> [{"instanceId":"*************#8085#DEFAULT#DEFAULT_GROUP@@discussion-service","ip":"*************","port":8085,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@discussion-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.302+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-6][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@learning-service -> [{"instanceId":"*************#8083#DEFAULT#DEFAULT_GROUP@@learning-service","ip":"*************","port":8083,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@learning-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.302+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@gateway-service -> []
[2m2025-08-01T18:35:54.302+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@user-service -> [{"instanceId":"*************#8081#DEFAULT#DEFAULT_GROUP@@user-service","ip":"*************","port":8081,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@user-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.302+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@recommendation-service -> [{"instanceId":"*************#8084#DEFAULT#DEFAULT_GROUP@@recommendation-service","ip":"*************","port":8084,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@recommendation-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.303+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.324+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:35:54.324+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:35:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:35:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:35:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:35:54.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:35:54.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:35:54.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:35:54.346+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36ma.c.n.d.NacosDiscoveryHeartBeatPublisher[0;39m [2m:[0;39m Start nacos heartBeat task scheduler.
[2m2025-08-01T18:35:54.348+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 10
[2m2025-08-01T18:35:54.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:35:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-9][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 10
[2m2025-08-01T18:35:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:35:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:35:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:35:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:35:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:35:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:35:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:35:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:35:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:35:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:35:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:35:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:35:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:35:54.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:35:54.360+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [           main][0;39m [2m[0;39m[36mc.l.gateway.GatewayApplication          [0;39m [2m:[0;39m Started GatewayApplication in 2.153 seconds (process running for 2.4)
[2m2025-08-01T18:35:54.361+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:35:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 10
[2m2025-08-01T18:35:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:35:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:35:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:35:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:35:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:35:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:35:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:35:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:35:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:35:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:35:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:35:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:35:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:35:54.823+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-28][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 50
[2m2025-08-01T18:35:54.823+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-28][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 50
[2m2025-08-01T18:35:54.825+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-29][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 51
[2m2025-08-01T18:35:54.825+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-29][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 51
[2m2025-08-01T18:35:54.827+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-30][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 53
[2m2025-08-01T18:35:54.827+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-30][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 53
[2m2025-08-01T18:35:54.828+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-31][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 52
[2m2025-08-01T18:35:54.828+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-31][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m new ips(1) service: DEFAULT_GROUP@@gateway-service -> [{"instanceId":"*************#8090#DEFAULT#DEFAULT_GROUP@@gateway-service","ip":"*************","port":8090,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.828+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-31][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@gateway-service -> [{"instanceId":"*************#8090#DEFAULT#DEFAULT_GROUP@@gateway-service","ip":"*************","port":8090,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:35:54.829+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [or-localhost-31][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 52
[2m2025-08-01T18:35:54.830+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-1][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 49
[2m2025-08-01T18:35:54.830+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-1][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 49
[2m2025-08-01T18:35:54.831+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-0][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 54
[2m2025-08-01T18:35:54.831+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-0][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 54
[2m2025-08-01T18:36:00.894+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-2][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 55
[2m2025-08-01T18:36:00.894+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [tor-localhost-2][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 55
[2m2025-08-01T18:36:24.132+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-1][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:36:24.132+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-1][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: POST http://localhost:8090/api/user/login] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:36:24.132+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-1][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [43a33876] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:36:24.133+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-1][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:36:24.158+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:36:24.159+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:36:24.159+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.159+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:36:24.160+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:36:24.160+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.160+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:36:24.160+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:36:24.161+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.162+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:36:24.162+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:36:24.163+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.163+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:36:24.163+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:36:24.163+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.164+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:36:24.164+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:36:24.164+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.165+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:36:24.165+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:36:24.165+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:36:24.165+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:36:24.166+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:36:24.167+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:36:24.168+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"69", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51342"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:24.170+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@7ab165a5', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=3.29791E-4, duration(nanos)=329791.0, startTimeNanos=77571405994500}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@653713ce', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.041064917, duration(nanos)=4.1064917E7, startTimeNanos=77571365318791}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"69", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51342"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:24.220+08:00[0;39m [31mERROR[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-01T18:36:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:36:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@7ab165a5', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.180283791, duration(nanos)=1.80283791E8, startTimeNanos=77571405994500}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@653713ce', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.220995917, duration(nanos)=2.20995917E8, startTimeNanos=77571365318791}'], parentObservation=null}}
[2m2025-08-01T18:36:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:36:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:36:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:36:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:36:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:36:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:36:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:36:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:36:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:36:24.360+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:36:24.360+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:36:24.360+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:36:33.720+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:36:33.721+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: POST http://localhost:8090/api/user/register] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:36:33.721+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [65ede5c2] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:36:33.721+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:36:33.721+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"171", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51416"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:33.721+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-4][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6b2a070f', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=4.2833E-5, duration(nanos)=42833.0, startTimeNanos=77580971701250}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6f158eb2', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.001225792, duration(nanos)=1225792.0, startTimeNanos=77580970568833}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"171", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51416"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:33.794+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:36:33.794+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6b2a070f', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.073020541, duration(nanos)=7.3020541E7, startTimeNanos=77580971701250}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6f158eb2', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.074202042, duration(nanos)=7.4202042E7, startTimeNanos=77580970568833}'], parentObservation=null}}
[2m2025-08-01T18:36:42.138+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:36:42.138+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: GET http://localhost:8090/api/user/profile] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:36:42.138+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [74a5f853] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:36:42.138+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:36:42.168+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", authorization:"Bearer eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************.CkF7SsrvecO1ubqcaOBEFxG659NyI_xOQ5gOomtlKmg", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51462"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:42.168+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-7][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='GET', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@56604773', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=4.9292E-5, duration(nanos)=49292.0, startTimeNanos=77589426226333}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='GET', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@5174f822', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.03139625, duration(nanos)=3.139625E7, startTimeNanos=77589394920208}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", authorization:"Bearer eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************.CkF7SsrvecO1ubqcaOBEFxG659NyI_xOQ5gOomtlKmg", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:51462"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:36:42.180+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:36:42.180+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-2][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='GET', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@56604773', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.012008875, duration(nanos)=1.2008875E7, startTimeNanos=77589426226333}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='GET', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@5174f822', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.043357208, duration(nanos)=4.3357208E7, startTimeNanos=77589394920208}'], parentObservation=null}}
[2m2025-08-01T18:36:54.331+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:36:54.332+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:36:54.332+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.332+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:36:54.332+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:36:54.332+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.333+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:36:54.333+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:36:54.333+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.333+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:36:54.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:36:54.335+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:36:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:36:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:36:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-11][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:37:24.333+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:37:24.334+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:37:24.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.337+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:37:24.337+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:37:24.337+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.338+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:37:24.338+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:37:24.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:37:24.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:37:24.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:37:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:37:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:37:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:37:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:37:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:37:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:37:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:37:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:37:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:37:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:37:54.336+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.337+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:37:54.337+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:37:54.338+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.338+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:37:54.338+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:37:54.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:37:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:37:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:37:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:37:54.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:37:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:37:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:37:54.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:37:54.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:37:54.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:38:24.339+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:38:24.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:38:24.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.340+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:38:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:38:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:38:24.341+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:38:24.342+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:38:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:38:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:38:24.343+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:38:24.344+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:38:24.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:38:24.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:38:24.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:38:24.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:38:24.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:38:39.208+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [io-8090-exec-10][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:38:39.208+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [io-8090-exec-10][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: POST http://localhost:8090/api/user/register] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:38:39.208+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [io-8090-exec-10][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [559ce5cb] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:38:39.208+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [io-8090-exec-10][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:38:39.209+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"175", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52042"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:39.209+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@211053e4', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=4.4167E-5, duration(nanos)=44167.0, startTimeNanos=77706477377208}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@fb7b8e3', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.001357709, duration(nanos)=1357709.0, startTimeNanos=77706476114916}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"175", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52042"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:39.283+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:38:39.284+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@211053e4', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.074842708, duration(nanos)=7.4842708E7, startTimeNanos=77706477377208}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/register'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@fb7b8e3', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.076136417, duration(nanos)=7.6136417E7, startTimeNanos=77706476114916}'], parentObservation=null}}
[2m2025-08-01T18:38:48.000+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:38:48.001+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: POST http://localhost:8090/api/user/login] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:38:48.001+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [7b11be89] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:38:48.002+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:38:48.006+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"68", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52103"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:48.009+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-3][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@7f2e3533', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=9.32083E-4, duration(nanos)=932083.0, startTimeNanos=77715276074500}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@3a2cb4b2', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.008559834, duration(nanos)=8559834.0, startTimeNanos=77715268517041}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"68", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52103"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:48.085+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:38:48.085+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@7f2e3533', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.07772125, duration(nanos)=7.772125E7, startTimeNanos=77715276074500}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@3a2cb4b2', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.08532875, duration(nanos)=8.532875E7, startTimeNanos=77715268517041}'], parentObservation=null}}
[2m2025-08-01T18:38:54.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:38:54.345+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-15][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:38:54.346+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.347+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:38:54.347+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:38:54.347+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.348+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:38:54.348+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:38:54.348+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:38:54.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:38:54.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:38:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:38:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:38:54.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:38:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:38:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:38:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:38:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:38:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:38:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:38:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-7][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:38:58.662+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:38:58.663+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: GET http://localhost:8090/api/user/profile] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:38:58.663+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [5d0a15cb] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:38:58.663+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:38:58.673+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", authorization:"Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.pngiI3XpXItYrSWzjparpq85fbcJk4eB84P1NA1vyZg", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52164"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:58.673+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-6][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='GET', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@423cc605', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=3.8666E-5, duration(nanos)=38666.0, startTimeNanos=77725941286625}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='GET', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@54d9cfd9', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.010550792, duration(nanos)=1.0550792E7, startTimeNanos=77725930812041}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", authorization:"Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.pngiI3XpXItYrSWzjparpq85fbcJk4eB84P1NA1vyZg", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52164"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:38:58.680+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:38:58.680+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-3][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='GET', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@423cc605', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.007624708, duration(nanos)=7624708.0, startTimeNanos=77725941286625}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='GET', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/profile'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@54d9cfd9', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.018136375, duration(nanos)=1.8136375E7, startTimeNanos=77725930812041}'], parentObservation=null}}
[2m2025-08-01T18:39:24.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:39:24.349+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:39:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:39:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:39:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.350+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:39:24.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:39:24.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:39:24.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:39:24.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:39:24.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:39:24.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:39:24.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-13][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:39:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:39:54.351+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:39:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:39:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:39:54.352+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.353+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:39:54.354+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:39:59.284+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-9][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Route matched: user-service
[2m2025-08-01T18:39:59.284+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-9][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m Mapping [Exchange: POST http://localhost:8090/api/user/login] to Route{id='user-service', uri=lb://user-service, order=0, predicate=Paths: [/api/user/**], match trailing slash: true, gatewayFilters=[], metadata={}}
[2m2025-08-01T18:39:59.284+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-9][0;39m [2m[0;39m[36mo.s.c.g.h.RoutePredicateHandlerMapping  [0;39m [2m:[0;39m [3b2609f8] Mapped to org.springframework.cloud.gateway.handler.FilteringWebHandler@4d94949a
[2m2025-08-01T18:39:59.284+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [nio-8090-exec-9][0;39m [2m[0;39m[36mo.s.c.g.handler.FilteringWebHandler     [0;39m [2m:[0;39m Sorted gatewayFilterFactories: [[GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RemoveCachedBodyFilter@6568f998}, order = -2147483648], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.AdaptCachedBodyGlobalFilter@d25e878}, order = -2147482648], [GatewayFilterAdapter{delegate=com.learningplatform.gateway.filter.JwtAuthenticationFilter@13516600}, order = -100], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyWriteResponseFilter@69f080ad}, order = -1], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardPathFilter@1537c744}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.GatewayMetricsFilter@********}, order = 0], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter@45d4421d}, order = 10000], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ReactiveLoadBalancerClientFilter@35d7386b}, order = 10150], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.LoadBalancerServiceInstanceCookieFilter@********}, order = 10151], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.WebsocketRoutingFilter@********}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.NettyRoutingFilter@718989fa}, order = **********], [GatewayFilterAdapter{delegate=org.springframework.cloud.gateway.filter.ForwardRoutingFilter@782fd504}, order = **********]]
[2m2025-08-01T18:39:59.285+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the HTTP request headers [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"68", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52446"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:39:59.285+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mg.f.h.o.ObservedRequestHttpHeadersFilter[0;39m [2m:[0;39m Client observation  {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6735d87f', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=4.275E-5, duration(nanos)=42750.0, startTimeNanos=77786553865083}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@78f5c2c4', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.001520708, duration(nanos)=1520708.0, startTimeNanos=77786552410250}'], parentObservation=null}} created for the request. New headers are [host:"localhost:8090", user-agent:"curl/8.7.1", accept:"*/*", content-type:"application/json", content-length:"68", Forwarded:"proto=http;host="localhost:8090";for="[0:0:0:0:0:0:0:1]:52446"", X-Forwarded-For:"0:0:0:0:0:0:0:1", X-Forwarded-Proto:"http", X-Forwarded-Port:"8090", X-Forwarded-Host:"localhost:8090"]
[2m2025-08-01T18:39:59.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-4][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m Will instrument the response
[2m2025-08-01T18:39:59.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ctor-http-nio-4][0;39m [2m[0;39m[36m.f.h.o.ObservedResponseHttpHeadersFilter[0;39m [2m:[0;39m The response was handled for observation {name=http.client.requests(null), error=null, context=name='http.client.requests', contextualName='null', error='null', lowCardinalityKeyValues=[http.method='POST', http.status_code='UNKNOWN', spring.cloud.gateway.route.id='user-service', spring.cloud.gateway.route.uri='lb://user-service'], highCardinalityKeyValues=[http.uri='http://localhost:8090/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@6735d87f', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.09783575, duration(nanos)=9.783575E7, startTimeNanos=77786553865083}'], parentObservation={name=http.server.requests(null), error=null, context=name='http.server.requests', contextualName='null', error='null', lowCardinalityKeyValues=[exception='none', method='POST', outcome='SUCCESS', status='200', uri='UNKNOWN'], highCardinalityKeyValues=[http.url='/api/user/login'], map=[class io.micrometer.core.instrument.Timer$Sample='io.micrometer.core.instrument.Timer$Sample@78f5c2c4', class io.micrometer.core.instrument.LongTaskTimer$Sample='SampleImpl{duration(seconds)=0.099327416, duration(nanos)=9.9327416E7, startTimeNanos=77786552410250}'], parentObservation=null}}
[2m2025-08-01T18:40:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:40:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:40:24.355+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:40:24.356+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:40:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:40:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:40:24.357+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:40:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:40:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:40:24.358+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:40:24.359+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-4][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:40:54.360+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:40:54.360+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:40:54.361+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.361+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:40:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:40:54.362+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:40:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:40:54.363+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.364+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:40:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:40:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:40:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:40:54.365+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:40:54.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:41:24.366+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:41:24.368+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:41:24.370+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:41:24.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:41:24.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:41:24.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:41:24.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:41:24.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:41:54.368+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:41:54.369+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.369+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:41:54.370+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:41:54.370+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:41:54.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:41:54.371+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:41:54.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:41:54.372+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:41:54.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.373+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:41:54.374+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:41:54.374+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.375+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:41:54.375+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:41:54.375+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:41:54.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:41:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:41:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:41:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:41:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:42:24.376+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-14][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:42:24.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:42:24.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:42:24.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:42:24.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:42:24.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:42:24.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:42:24.382+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:42:24.382+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.382+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:42:24.382+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:42:24.383+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:42:24.384+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:42:24.385+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:42:24.385+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:42:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:42:54.377+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:42:54.378+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.378+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:42:54.378+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:42:54.379+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:42:54.380+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:42:54.381+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:43:24.386+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:43:24.386+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:43:24.387+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.387+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:43:24.387+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:43:24.387+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.388+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:43:24.388+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:43:24.388+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.388+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:43:24.388+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:43:24.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:43:24.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:43:24.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:43:24.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:43:24.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:43:24.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:43:24.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:43:24.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:43:24.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:43:24.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:43:24.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:43:54.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:43:54.389+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-12][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:43:54.390+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:43:54.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:43:54.391+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:43:54.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:43:54.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:43:54.392+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:43:54.393+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.393+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:43:54.393+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:43:54.394+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:43:54.395+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:43:54.396+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-3][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:44:24.398+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:44:24.398+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:44:24.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:44:24.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:44:24.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:44:24.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:44:24.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:44:24.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:44:24.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:44:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:44:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:44:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:44:24.404+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:44:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:44:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:44:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-18][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:44:54.398+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-19][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 11
[2m2025-08-01T18:44:54.398+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying {pattern=/course-service/**} to Path
[2m2025-08-01T18:44:54.399+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_course-service applying filter {regexp=/course-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.399+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_course-service
[2m2025-08-01T18:44:54.399+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying {pattern=/discussion-service/**} to Path
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_discussion-service applying filter {regexp=/discussion-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_discussion-service
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying {pattern=/learning-service/**} to Path
[2m2025-08-01T18:44:54.400+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_learning-service applying filter {regexp=/learning-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_learning-service
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying {pattern=/user-service/**} to Path
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_user-service applying filter {regexp=/user-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_user-service
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying {pattern=/recommendation-service/**} to Path
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_recommendation-service applying filter {regexp=/recommendation-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_recommendation-service
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:44:54.401+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:44:54.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:44:54.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:44:54.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:44:54.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:44:54.402+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:45:03.137+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-313][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 57
[2m2025-08-01T18:45:03.137+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-313][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:45:03.137+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-313][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@course-service -> []
[2m2025-08-01T18:45:03.138+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-313][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 57
[2m2025-08-01T18:45:07.995+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-314][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 58
[2m2025-08-01T18:45:07.996+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-314][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@user-service -> [{"instanceId":"*************#8081#DEFAULT#DEFAULT_GROUP@@user-service","ip":"*************","port":8081,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@user-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:45:07.996+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-314][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@user-service -> []
[2m2025-08-01T18:45:07.996+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-314][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 58
[2m2025-08-01T18:45:13.151+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-315][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 59
[2m2025-08-01T18:45:13.151+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-315][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@learning-service -> [{"instanceId":"*************#8083#DEFAULT#DEFAULT_GROUP@@learning-service","ip":"*************","port":8083,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@learning-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:45:13.152+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-315][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@learning-service -> []
[2m2025-08-01T18:45:13.152+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-315][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 59
[2m2025-08-01T18:45:17.902+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-316][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 60
[2m2025-08-01T18:45:17.902+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-316][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@recommendation-service -> [{"instanceId":"*************#8084#DEFAULT#DEFAULT_GROUP@@recommendation-service","ip":"*************","port":8084,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@recommendation-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:45:17.902+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-316][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@recommendation-service -> []
[2m2025-08-01T18:45:17.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-316][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 60
[2m2025-08-01T18:45:22.551+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-317][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Receive server push request, request = NotifySubscriberRequest, requestId = 61
[2m2025-08-01T18:45:22.551+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-317][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@discussion-service -> [{"instanceId":"*************#8085#DEFAULT#DEFAULT_GROUP@@discussion-service","ip":"*************","port":8085,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@discussion-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T18:45:22.551+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-317][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@discussion-service -> []
[2m2025-08-01T18:45:22.552+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-317][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [2dd423aa-e75b-463b-a88c-903252d08782] Ack server push request, request = NotifySubscriberRequest, requestId = 61
[2m2025-08-01T18:45:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [undedElastic-17][0;39m [2m[0;39m[36mo.s.c.g.filter.GatewayMetricsFilter     [0;39m [2m:[0;39m New routes count: 6
[2m2025-08-01T18:45:24.403+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying {pattern=/gateway-service/**} to Path
[2m2025-08-01T18:45:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition ReactiveCompositeDiscoveryClient_gateway-service applying filter {regexp=/gateway-service/?(?<remaining>.*), replacement=/${remaining}} to RewritePath
[2m2025-08-01T18:45:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: ReactiveCompositeDiscoveryClient_gateway-service
[2m2025-08-01T18:45:24.405+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition user-service applying {_genkey_0=/api/user/**} to Path
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: user-service
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition course-service applying {_genkey_0=/api/course/**} to Path
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: course-service
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition learning-service applying {_genkey_0=/api/learning/**} to Path
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: learning-service
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition recommendation-service applying {_genkey_0=/api/recommendation/**} to Path
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: recommendation-service
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition discussion-service applying {_genkey_0=/api/discussion/**} to Path
[2m2025-08-01T18:45:24.406+08:00[0;39m [32mDEBUG[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [oundedElastic-8][0;39m [2m[0;39m[36mo.s.c.g.r.RouteDefinitionRouteLocator   [0;39m [2m:[0;39m RouteDefinition matched: discussion-service
[2m2025-08-01T18:45:26.809+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Start destroying Publisher
[2m2025-08-01T18:45:26.809+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Start destroying common HttpClient
[2m2025-08-01T18:45:26.809+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Destruction of the end
[2m2025-08-01T18:45:26.809+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Destruction of the end
[2m2025-08-01T18:45:28.898+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registering from Nacos Server now...
[2m2025-08-01T18:45:28.898+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [DEREGISTER-SERVICE] public deregistering service gateway-service with instance: Instance{instanceId='null', ip='*************', port=8090, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
[2m2025-08-01T18:45:28.902+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registration finished.
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:28.903+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[2m2025-08-01T18:45:28.903+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:28.903+08:00[0;39m [33m WARN[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown rpc client, set status to shutdown
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@b34c7c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[2m2025-08-01T18:45:28.903+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Close current connection 1754044554103_127.0.0.1_51169
[2m2025-08-01T18:45:28.904+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [r-localhost-324][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754044554103_127.0.0.1_51169]Ignore complete event,isRunning:false,isAbandon=false
[2m2025-08-01T18:45:28.906+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75c33608[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 360]
[2m2025-08-01T18:45:28.906+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@7c0e4e4e[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 191]
[2m2025-08-01T18:45:28.907+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m [null] CredentialWatcher is stopped
[2m2025-08-01T18:45:28.907+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialService  [0;39m [2m:[0;39m [null] CredentialService is freed
[2m2025-08-01T18:45:28.907+08:00[0;39m [32m INFO[0;39m [35m29796[0;39m [2m---[0;39m [2m[gateway-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
