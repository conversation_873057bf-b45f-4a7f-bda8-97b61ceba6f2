#!/bin/bash

echo "=== 讨论服务API测试 ==="

# 1. 健康检查
echo "1. 健康检查:"
curl -s http://localhost:8085/health | jq .
echo -e "\n"

# 2. 创建讨论主题（使用课程ID 1）
echo "2. 创建讨论主题:"
TOPIC_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/topics \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 1" \
  -d '{
    "courseId": 1,
    "title": "Spring Boot学习讨论",
    "content": "大家在学习Spring Boot过程中遇到了什么问题？"
  }')
echo $TOPIC_RESPONSE | jq .
echo -e "\n"

# 提取主题ID（如果创建成功）
TOPIC_ID=$(echo $TOPIC_RESPONSE | jq -r '.data.id // empty')

if [ ! -z "$TOPIC_ID" ]; then
    echo "主题创建成功，ID: $TOPIC_ID"
    
    # 3. 获取课程讨论主题列表
    echo "3. 获取课程讨论主题列表:"
    curl -s "http://localhost:8085/api/discussion/topics/course/1?page=1&size=10" | jq .
    echo -e "\n"
    
    # 4. 创建回复
    echo "4. 创建回复:"
    REPLY_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/replies \
      -H "Content-Type: application/json" \
      -H "X-User-Id: 1" \
      -d "{
        \"topicId\": $TOPIC_ID,
        \"content\": \"我在配置数据库连接时遇到了问题\"
      }")
    echo $REPLY_RESPONSE | jq .
    echo -e "\n"
    
    # 5. 获取主题回复列表
    echo "5. 获取主题回复列表:"
    curl -s "http://localhost:8085/api/discussion/replies/topic/$TOPIC_ID?page=1&size=10" | jq .
    echo -e "\n"
    
    # 6. 获取主题详情
    echo "6. 获取主题详情:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC_ID" | jq .
    echo -e "\n"
else
    echo "主题创建失败，跳过后续测试"
fi

echo "=== 测试完成 ==="