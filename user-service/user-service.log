[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------< com.learningplatform:user-service >------------------
[INFO] Building User Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ user-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ user-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ user-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ user-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ user-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:00:01.375+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:00:01.393+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:00:01.395+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Starting UserServiceApplication using Java 23.0.2 with PID 19237 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service)
[2m2025-08-01T18:00:01.395+08:00[0;39m [32mDEBUG[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:00:01.396+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:00:01.641+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=43f6f7d7-1e47-3aac-b09c-758343b790b5
[2m2025-08-01T18:00:01.747+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8081 (http)
[2m2025-08-01T18:00:01.751+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:00:01.751+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:00:01.768+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:00:01.769+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 352 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Initialization Sequence datacenterId:0 workerId:30
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
[2m2025-08-01T18:00:02.023+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8081 (http) with context path ''
[2m2025-08-01T18:00:02.025+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:00:02.025+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:00:02.025+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:00:02.025+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:00:02.034+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:00:02.034+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:00:02.034+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:00:02.037+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:00:02.037+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:00:02.051+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:00:02.054+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 0908bf18-dafd-412a-a3c8-edb742073e45
[2m2025-08-01T18:00:02.059+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:00:02.059+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:00:02.059+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:00:02.060+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:00:02.071+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:00:02.231+08:00[0;39m [31mERROR[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754042402201_127.0.0.1_59286]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:00:02.329+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Success to connect to server [localhost:8848] on start up, connectionId = 1754042402201_127.0.0.1_59286
[2m2025-08-01T18:00:02.329+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Notify connected event to listeners.
[2m2025-08-01T18:00:02.329+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:00:02.329+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:00:02.330+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [0908bf18-dafd-412a-a3c8-edb742073e45] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00007f80016970d8
[2m2025-08-01T18:00:02.330+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service user-service with instance Instance{instanceId='null', ip='*************', port=8081, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:00:02.338+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP user-service *************:8081 register finished
[2m2025-08-01T18:00:02.343+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Started UserServiceApplication in 1.089 seconds (process running for 1.211)
[2m2025-08-01T18:00:30.767+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T18:00:30.772+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T18:00:30.772+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 0 ms
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62f5c9c2] was not registered for synchronization because synchronization is not active
[2m2025-08-01T18:00:30.792+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Starting...
[2m2025-08-01T18:00:30.862+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6433e808
[2m2025-08-01T18:00:30.862+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Start completed.
JDBC Connection [HikariProxyConnection@1033745914 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62f5c9c2]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@149cda94] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2126331401 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@149cda94]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@416e9a8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@181471066 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@416e9a8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ff36e82] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@20218124 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ff36e82]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62595436] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1822340621 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62595436]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a01cf53] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1422277219 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a01cf53]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f202b09] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@568836253 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2f202b09]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f6aa88] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@238114022 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f6aa88]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d016488] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@868654424 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d016488]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42864c4d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1427825670 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42864c4d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68917e4] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1957897691 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68917e4]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bec0d61] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2021979677 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bec0d61]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40098b37] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1419410122 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40098b37]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da3bc36] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@91465040 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 1(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2da3bc36]
[2m2025-08-01T18:28:35.921+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [io-8081-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserInfoResponse> com.learningplatform.user.controller.AuthController.register(com.learningplatform.user.dto.UserRegisterRequest): [Field error in object 'userRegisterRequest' on field 'confirmPassword': rejected value [null]; codes [NotBlank.userRegisterRequest.confirmPassword,NotBlank.confirmPassword,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userRegisterRequest.confirmPassword,confirmPassword]; arguments []; default message [confirmPassword]]; default message [确认密码不能为空]] ]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c4233f9] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1826025496 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: testuser(String)
<==    Columns: COUNT(*) > 0
<==        Row: 1
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c4233f9]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f6047] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@734261806 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: frontendtest(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f6047]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60fdd785] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@460058245 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60fdd785]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114809d1] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1622177432 wrapping com.mysql.cj.jdbc.ConnectionImpl@6433e808] will not be managed by Spring
==>  Preparing: INSERT INTO users ( username, email, password, nickname, role, status ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: frontendtest(String), <EMAIL>(String), $2a$10$zQT9GIKJwwQnR.4gW9acSe12Fo/N6CY4qdCDgjmpqzKp183LWWsJ2(String), 前端测试用户(String), STUDENT(String), ACTIVE(String)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114809d1]
[2m2025-08-01T18:29:39.835+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@557638b2] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1538378860 wrapping com.mysql.cj.jdbc.ConnectionImpl@5f8e53c2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: frontendtest(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 2, frontendtest, <EMAIL>, $2a$10$zQT9GIKJwwQnR.4gW9acSe12Fo/N6CY4qdCDgjmpqzKp183LWWsJ2, 前端测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 18:29:32, 2025-08-01 18:29:32
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@557638b2]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75b5867b] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1600041196 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: directtest(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75b5867b]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3cce22b2] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@373111831 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3cce22b2]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@584df527] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@358319918 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: INSERT INTO users ( username, email, password, nickname, role, status ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: directtest(String), <EMAIL>(String), $2a$10$F9mO4C05KyRYdmMYE2ok/ufWTnh2B/dHbItIGLnexde3sQ3eEkJsW(String), 直接测试用户(String), STUDENT(String), ACTIVE(String)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@584df527]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53f555f3] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@495803998 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: frontendtest(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 2, frontendtest, <EMAIL>, $2a$10$zQT9GIKJwwQnR.4gW9acSe12Fo/N6CY4qdCDgjmpqzKp183LWWsJ2, 前端测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 18:29:32, 2025-08-01 18:29:32
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53f555f3]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4222df63] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@558756312 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: gatewaytest3(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4222df63]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28848a66] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2080997842 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@28848a66]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25274a75] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@696001293 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: INSERT INTO users ( username, email, password, nickname, role, status ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: gatewaytest3(String), <EMAIL>(String), $2a$10$KWRSJWUCb8nBbEuUso5aSuJQPZqu60Ih5HKh69ir8yWICwilv9QCy(String), 网关测试用户3(String), STUDENT(String), ACTIVE(String)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@25274a75]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32f99399] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1817962962 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 2(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 2, frontendtest, <EMAIL>, $2a$10$zQT9GIKJwwQnR.4gW9acSe12Fo/N6CY4qdCDgjmpqzKp183LWWsJ2, 前端测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 18:29:32, 2025-08-01 18:29:32
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32f99399]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cf1db56] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@550704940 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: realtest001(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cf1db56]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@392e0084] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2068273052 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@392e0084]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@132acf17] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1523173334 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: INSERT INTO users ( username, email, password, nickname, role, status ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: realtest001(String), <EMAIL>(String), $2a$10$y1cgIhPQZjfIo1omQL9YBuM/UIGBUpETt1dqgPJCkD4dw28QL/ZMC(String), 真实测试用户001(String), STUDENT(String), ACTIVE(String)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@132acf17]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15fb3058] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1104416483 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: realtest001(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 5, realtest001, <EMAIL>, $2a$10$y1cgIhPQZjfIo1omQL9YBuM/UIGBUpETt1dqgPJCkD4dw28QL/ZMC, 真实测试用户001, null, null, STUDENT, ACTIVE, 2025-08-01 18:38:39, 2025-08-01 18:38:39
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15fb3058]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4d8bc296] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@229870656 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT id,username,email,password,nickname,avatar_url,phone,role,status,created_at,updated_at FROM users WHERE id=?
==> Parameters: 5(Long)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 5, realtest001, <EMAIL>, $2a$10$y1cgIhPQZjfIo1omQL9YBuM/UIGBUpETt1dqgPJCkD4dw28QL/ZMC, 真实测试用户001, null, null, STUDENT, ACTIVE, 2025-08-01 18:38:39, 2025-08-01 18:38:39
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4d8bc296]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@dc310de] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1789400132 wrapping com.mysql.cj.jdbc.ConnectionImpl@db53571] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: realtest001(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 5, realtest001, <EMAIL>, $2a$10$y1cgIhPQZjfIo1omQL9YBuM/UIGBUpETt1dqgPJCkD4dw28QL/ZMC, 真实测试用户001, null, null, STUDENT, ACTIVE, 2025-08-01 18:38:39, 2025-08-01 18:38:39
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@dc310de]
[2m2025-08-01T18:45:07.466+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Start destroying common HttpClient
[2m2025-08-01T18:45:07.466+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Start destroying Publisher
[2m2025-08-01T18:45:07.466+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Destruction of the end
[2m2025-08-01T18:45:07.466+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Destruction of the end
[2m2025-08-01T18:45:07.469+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registering from Nacos Server now...
[2m2025-08-01T18:45:07.469+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [DEREGISTER-SERVICE] public deregistering service user-service with instance: Instance{instanceId='null', ip='*************', port=8081, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
[2m2025-08-01T18:45:07.486+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registration finished.
[2m2025-08-01T18:45:07.488+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[2m2025-08-01T18:45:07.489+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[2m2025-08-01T18:45:07.490+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[2m2025-08-01T18:45:07.490+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[2m2025-08-01T18:45:07.490+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[2m2025-08-01T18:45:07.490+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[2m2025-08-01T18:45:07.491+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:07.491+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[2m2025-08-01T18:45:07.491+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:07.491+08:00[0;39m [33m WARN[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[2m2025-08-01T18:45:07.491+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown rpc client, set status to shutdown
[2m2025-08-01T18:45:07.492+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@35fa450f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[2m2025-08-01T18:45:07.493+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Close current connection 1754042402201_127.0.0.1_59286
[2m2025-08-01T18:45:07.493+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [-localhost-1098][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754042402201_127.0.0.1_59286]Ignore complete event,isRunning:false,isAbandon=false
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d91fa02[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1099]
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@7d25913[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 900]
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m [null] CredentialWatcher is stopped
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialService  [0;39m [2m:[0;39m [null] CredentialService is freed
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
[2m2025-08-01T18:45:07.494+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Shutdown initiated...
[2m2025-08-01T18:45:07.495+08:00[0;39m [32m INFO[0;39m [35m19237[0;39m [2m---[0;39m [2m[user-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Shutdown completed.
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  45:07 min
[INFO] Finished at: 2025-08-01T18:45:07+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project user-service: Process terminated with exit code: 143 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
