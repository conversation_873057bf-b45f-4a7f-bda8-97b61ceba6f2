# User Service API Documentation

## Base URL
```
http://localhost:8081/api/user
```

## Authentication APIs

### 1. User Registration
**Endpoint:** `POST /register`

**Request Body:**
```json
{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "Test User"
}
```

**Response (Success):**
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "Test User",
        "avatarUrl": null,
        "phone": null,
        "role": "STUDENT",
        "status": "ACTIVE",
        "createdAt": "2025-08-01T15:30:00"
    },
    "timestamp": "2025-08-01T15:30:00"
}
```

**Response (Error):**
```json
{
    "success": false,
    "code": "1003",
    "message": "用户名已存在",
    "data": null,
    "timestamp": "2025-08-01T15:30:00"
}
```

### 2. User Login
**Endpoint:** `POST /login`

**Request Body:**
```json
{
    "usernameOrEmail": "testuser",
    "password": "password123"
}
```

**Response (Success):**
```json
{
    "success": true,
    "code": "200", 
    "message": "操作成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOjEsInVzZXJuYW1lIjoidGVzdHVzZXIiLCJyb2xlIjoiU1RVREVOVCJ9...",
        "userInfo": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "nickname": "Test User",
            "avatarUrl": null,
            "phone": null,
            "role": "STUDENT",
            "status": "ACTIVE",
            "createdAt": "2025-08-01T15:30:00"
        }
    },
    "timestamp": "2025-08-01T15:30:00"
}
```

**Response (Error):**
```json
{
    "success": false,
    "code": "1002",
    "message": "密码错误",
    "data": null,
    "timestamp": "2025-08-01T15:30:00"
}
```

## User Management APIs

### 3. Get User Profile
**Endpoint:** `GET /profile`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response (Success):**
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "Test User",
        "avatarUrl": null,
        "phone": null,
        "role": "STUDENT",
        "status": "ACTIVE",
        "createdAt": "2025-08-01T15:30:00"
    },
    "timestamp": "2025-08-01T15:30:00"
}
```

### 4. Update User Profile
**Endpoint:** `PUT /profile`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "nickname": "Updated Nickname",
    "avatarUrl": "https://example.com/avatar.jpg",
    "phone": "1234567890"
}
```

**Response (Success):**
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "Updated Nickname",
        "avatarUrl": "https://example.com/avatar.jpg",
        "phone": "1234567890",
        "role": "STUDENT",
        "status": "ACTIVE",
        "createdAt": "2025-08-01T15:30:00"
    },
    "timestamp": "2025-08-01T15:30:00"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 1001 | 用户不存在 |
| 1002 | 密码错误 |
| 1003 | 用户名或邮箱已存在 |
| 1004 | 权限不足或认证失败 |

## Testing with cURL

### Register a new user:
```bash
curl -X POST http://localhost:8081/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123",
    "nickname": "Test User"
  }'
```

### Login:
```bash
curl -X POST http://localhost:8081/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "testuser",
    "password": "password123"
  }'
```

### Get user profile:
```bash
curl -X GET http://localhost:8081/api/user/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Update user profile:
```bash
curl -X PUT http://localhost:8081/api/user/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "nickname": "Updated Test User",
    "phone": "1234567890"
  }'
```