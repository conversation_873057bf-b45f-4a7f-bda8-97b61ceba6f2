2025-08-02 00:04:59,412 INFO Client connection 1754058863006_127.0.0.1_63254 disconnect, remove instances and subscribers

2025-08-02 00:05:09,831 INFO Client connection 1754064309804_127.0.0.1_63213 connect

2025-08-02 00:05:09,941 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=18}, 1754064309804_127.0.0.1_63213

2025-08-02 00:06:25,107 INFO Client connection 1754064309804_127.0.0.1_63213 disconnect, remove instances and subscribers

2025-08-02 00:06:36,816 INFO Client connection 1754064396786_127.0.0.1_63717 connect

2025-08-02 00:06:36,929 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=20}, 1754064396786_127.0.0.1_63717

2025-08-02 00:10:57,003 INFO Client connection 1754057710652_127.0.0.1_58891 disconnect, remove instances and subscribers

2025-08-02 00:11:04,897 INFO Client connection 1754064664870_127.0.0.1_65169 connect

2025-08-02 00:11:05,014 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=18}, 1754064664870_127.0.0.1_65169

2025-08-02 00:20:47,581 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, 1754055759425_127.0.0.1_64131

2025-08-02 00:20:47,585 INFO Client connection 1754055759425_127.0.0.1_64131 disconnect, remove instances and subscribers

2025-08-02 00:20:47,597 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, 1754062394721_127.0.0.1_59464

2025-08-02 00:20:47,597 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=13}, 1754062475327_127.0.0.1_59592

2025-08-02 00:20:47,600 INFO Client connection 1754062394721_127.0.0.1_59464 disconnect, remove instances and subscribers

2025-08-02 00:20:47,600 INFO Client connection 1754062475327_127.0.0.1_59592 disconnect, remove instances and subscribers

2025-08-02 00:20:47,601 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754063129068_127.0.0.1_60434

2025-08-02 00:20:47,606 INFO Client connection 1754063129068_127.0.0.1_60434 disconnect, remove instances and subscribers

2025-08-02 00:20:47,609 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=21}, 1754064396786_127.0.0.1_63717

2025-08-02 00:20:47,615 INFO Client connection 1754064396786_127.0.0.1_63717 disconnect, remove instances and subscribers

2025-08-02 00:20:49,689 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}, 1754064664870_127.0.0.1_65169

2025-08-02 00:20:49,696 INFO Client connection 1754064664870_127.0.0.1_65169 disconnect, remove instances and subscribers

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@user-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@recommendation-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@learning-service] services are automatically cleaned

2025-08-02 00:21:53,131 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-02 00:22:48,056 INFO Client connection 1754065368019_127.0.0.1_52593 connect

2025-08-02 00:22:48,167 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=0}, 1754065368019_127.0.0.1_52593

2025-08-02 00:22:50,374 INFO Client connection 1754065370344_127.0.0.1_52608 connect

2025-08-02 00:22:50,488 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754065370344_127.0.0.1_52608

2025-08-02 00:25:10,889 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, 1754065368019_127.0.0.1_52593

2025-08-02 00:25:10,893 INFO Client connection 1754065368019_127.0.0.1_52593 disconnect, remove instances and subscribers

2025-08-02 00:25:17,868 INFO Client connection 1754065517844_127.0.0.1_53417 connect

2025-08-02 00:25:17,982 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, 1754065517844_127.0.0.1_53417

2025-08-02 00:28:18,611 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, 1754065517844_127.0.0.1_53417

2025-08-02 00:28:18,615 INFO Client connection 1754065517844_127.0.0.1_53417 disconnect, remove instances and subscribers

2025-08-02 00:28:21,834 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754065370344_127.0.0.1_52608

2025-08-02 00:28:21,841 INFO Client connection 1754065370344_127.0.0.1_52608 disconnect, remove instances and subscribers

2025-08-02 00:28:37,032 INFO Client connection 1754065717007_127.0.0.1_54379 connect

2025-08-02 00:28:37,146 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=0}, 1754065717007_127.0.0.1_54379

2025-08-02 00:28:43,184 INFO Client connection 1754065723160_127.0.0.1_54390 connect

2025-08-02 00:28:43,297 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754065723160_127.0.0.1_54390

2025-08-02 00:28:48,746 INFO Client connection 1754065728719_127.0.0.1_54414 connect

2025-08-02 00:28:48,853 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, 1754065728719_127.0.0.1_54414

2025-08-02 00:28:52,344 INFO Client connection 1754065732317_127.0.0.1_54420 connect

2025-08-02 00:28:52,453 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=0}, 1754065732317_127.0.0.1_54420

2025-08-02 00:28:57,501 INFO Client connection 1754065737474_127.0.0.1_54435 connect

2025-08-02 00:28:57,611 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754065737474_127.0.0.1_54435

2025-08-02 00:29:01,993 INFO Client connection 1754065741963_127.0.0.1_54444 connect

2025-08-02 00:29:02,105 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=2}, 1754065741963_127.0.0.1_54444

2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
2025-08-02 00:29:39,179 ERROR [TASK-FAILED] java.lang.InterruptedException

java.lang.InterruptedException: null
	at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1727)
	at java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
	at com.alibaba.nacos.common.task.engine.TaskExecuteWorker$InnerWorker.run(TaskExecuteWorker.java:118)
