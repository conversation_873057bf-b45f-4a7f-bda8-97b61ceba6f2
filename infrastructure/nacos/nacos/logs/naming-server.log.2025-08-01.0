2025-08-01 15:06:20,763 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-08-01 15:06:20,763 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-08-01 15:06:21,352 INFO Load instance extension handler []

2025-08-01 15:20:24,842 INFO Client connection 1754032824785_127.0.0.1_53787 connect

2025-08-01 15:20:24,947 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754032824785_127.0.0.1_53787

2025-08-01 15:45:37,946 INFO Client connection 1754034337917_127.0.0.1_58255 connect

2025-08-01 15:45:38,054 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754034337917_127.0.0.1_58255

2025-08-01 15:53:29,197 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754034337917_127.0.0.1_58255

2025-08-01 15:53:29,204 INFO Client connection 1754034337917_127.0.0.1_58255 disconnect, remove instances and subscribers

2025-08-01 15:53:47,408 INFO Client connection 1754034827382_127.0.0.1_59137 connect

2025-08-01 15:53:47,522 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754034827382_127.0.0.1_59137

2025-08-01 16:06:04,336 INFO Client connection 1754035564309_127.0.0.1_61201 connect

2025-08-01 16:06:04,445 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=0}, 1754035564309_127.0.0.1_61201

2025-08-01 16:10:43,206 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, 1754035564309_127.0.0.1_61201

2025-08-01 16:10:43,210 INFO Client connection 1754035564309_127.0.0.1_61201 disconnect, remove instances and subscribers

2025-08-01 16:11:31,575 INFO Client connection 1754035891547_127.0.0.1_61635 connect

2025-08-01 16:11:31,684 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, 1754035891547_127.0.0.1_61635

2025-08-01 16:12:14,873 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, 1754035891547_127.0.0.1_61635

2025-08-01 16:12:14,877 INFO Client connection 1754035891547_127.0.0.1_61635 disconnect, remove instances and subscribers

2025-08-01 16:12:55,073 INFO Client connection 1754035975042_127.0.0.1_61800 connect

2025-08-01 16:12:55,182 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, 1754035975042_127.0.0.1_61800

2025-08-01 16:31:36,971 INFO Client connection 1754037096943_127.0.0.1_64539 connect

2025-08-01 16:31:37,081 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=0}, 1754037096943_127.0.0.1_64539

2025-08-01 16:33:13,295 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, 1754037096943_127.0.0.1_64539

2025-08-01 16:33:13,302 INFO Client connection 1754037096943_127.0.0.1_64539 disconnect, remove instances and subscribers

2025-08-01 16:33:22,177 INFO Client connection 1754037202150_127.0.0.1_64943 connect

2025-08-01 16:33:22,289 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=2}, 1754037202150_127.0.0.1_64943

2025-08-01 16:37:39,594 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=3}, 1754037202150_127.0.0.1_64943

2025-08-01 16:37:39,597 INFO Client connection 1754037202150_127.0.0.1_64943 disconnect, remove instances and subscribers

2025-08-01 16:37:51,315 INFO Client connection 1754037471288_127.0.0.1_65519 connect

2025-08-01 16:37:51,427 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=4}, 1754037471288_127.0.0.1_65519

2025-08-01 16:50:16,986 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=5}, 1754037471288_127.0.0.1_65519

2025-08-01 16:50:16,990 INFO Client connection 1754037471288_127.0.0.1_65519 disconnect, remove instances and subscribers

2025-08-01 16:50:28,145 INFO Client connection 1754038228119_127.0.0.1_50467 connect

2025-08-01 16:50:28,257 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=6}, 1754038228119_127.0.0.1_50467

2025-08-01 17:28:29,796 INFO Client connection 1754040509771_127.0.0.1_55028 connect

2025-08-01 17:28:29,907 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754040509771_127.0.0.1_55028

2025-08-01 17:28:44,574 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754040509771_127.0.0.1_55028

2025-08-01 17:28:44,577 INFO Client connection 1754040509771_127.0.0.1_55028 disconnect, remove instances and subscribers

2025-08-01 17:29:13,818 INFO Client connection 1754040553789_127.0.0.1_55130 connect

2025-08-01 17:29:13,928 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754040553789_127.0.0.1_55130

2025-08-01 17:30:04,368 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754040553789_127.0.0.1_55130

2025-08-01 17:30:04,372 INFO Client connection 1754040553789_127.0.0.1_55130 disconnect, remove instances and subscribers

2025-08-01 17:31:03,965 INFO Client connection 1754040663940_127.0.0.1_55421 connect

2025-08-01 17:31:04,075 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, 1754040663940_127.0.0.1_55421

2025-08-01 17:31:59,939 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, 1754040663940_127.0.0.1_55421

2025-08-01 17:31:59,943 INFO Client connection 1754040663940_127.0.0.1_55421 disconnect, remove instances and subscribers

2025-08-01 17:32:14,286 INFO Client connection 1754040734260_127.0.0.1_55629 connect

2025-08-01 17:32:14,396 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, 1754040734260_127.0.0.1_55629

2025-08-01 17:32:51,105 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=7}, 1754040734260_127.0.0.1_55629

2025-08-01 17:32:51,110 INFO Client connection 1754040734260_127.0.0.1_55629 disconnect, remove instances and subscribers

2025-08-01 17:33:00,637 INFO Client connection 1754040780608_127.0.0.1_55750 connect

2025-08-01 17:33:00,746 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=8}, 1754040780608_127.0.0.1_55750

2025-08-01 17:33:31,751 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, 1754040780608_127.0.0.1_55750

2025-08-01 17:33:31,755 INFO Client connection 1754040780608_127.0.0.1_55750 disconnect, remove instances and subscribers

2025-08-01 17:33:41,828 INFO Client connection 1754040821800_127.0.0.1_55922 connect

2025-08-01 17:33:41,938 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, 1754040821800_127.0.0.1_55922

2025-08-01 17:34:18,041 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, 1754040821800_127.0.0.1_55922

2025-08-01 17:34:18,044 INFO Client connection 1754040821800_127.0.0.1_55922 disconnect, remove instances and subscribers

2025-08-01 17:35:51,727 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-01 17:36:16,809 INFO Client connection 1754040976781_127.0.0.1_56281 connect

2025-08-01 17:36:16,920 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754040976781_127.0.0.1_56281

2025-08-01 17:38:53,857 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754040976781_127.0.0.1_56281

2025-08-01 17:38:53,860 INFO Client connection 1754040976781_127.0.0.1_56281 disconnect, remove instances and subscribers

2025-08-01 17:38:53,861 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754032824785_127.0.0.1_53787

2025-08-01 17:38:53,864 INFO Client connection 1754032824785_127.0.0.1_53787 disconnect, remove instances and subscribers

2025-08-01 17:40:51,750 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-01 17:40:51,750 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-01 17:45:47,138 INFO Client connection 1754041547112_127.0.0.1_57392 connect

2025-08-01 17:45:47,249 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754041547112_127.0.0.1_57392

2025-08-01 17:49:04,488 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754041547112_127.0.0.1_57392

2025-08-01 17:49:04,491 INFO Client connection 1754041547112_127.0.0.1_57392 disconnect, remove instances and subscribers

2025-08-01 17:49:16,195 INFO Client connection 1754041756169_127.0.0.1_57809 connect

2025-08-01 17:49:16,301 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754041756169_127.0.0.1_57809

2025-08-01 17:51:12,824 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, 1754041756169_127.0.0.1_57809

2025-08-01 17:51:12,831 INFO Client connection 1754041756169_127.0.0.1_57809 disconnect, remove instances and subscribers

2025-08-01 17:51:20,906 INFO Client connection 1754041880879_127.0.0.1_58005 connect

2025-08-01 17:51:21,017 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, 1754041880879_127.0.0.1_58005

2025-08-01 17:52:52,270 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, 1754041880879_127.0.0.1_58005

2025-08-01 17:52:52,274 INFO Client connection 1754041880879_127.0.0.1_58005 disconnect, remove instances and subscribers

2025-08-01 17:53:05,200 INFO Client connection 1754041985175_127.0.0.1_58376 connect

2025-08-01 17:53:05,313 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, 1754041985175_127.0.0.1_58376

2025-08-01 17:58:57,957 INFO Client connection 1754042337930_127.0.0.1_59095 connect

2025-08-01 17:58:58,069 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=0}, 1754042337930_127.0.0.1_59095

2025-08-01 17:59:50,095 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=1}, 1754042337930_127.0.0.1_59095

2025-08-01 17:59:50,099 INFO Client connection 1754042337930_127.0.0.1_59095 disconnect, remove instances and subscribers

2025-08-01 18:00:02,226 INFO Client connection 1754042402201_127.0.0.1_59286 connect

2025-08-01 18:00:02,336 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=2}, 1754042402201_127.0.0.1_59286

2025-08-01 18:01:51,373 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=7}, 1754041985175_127.0.0.1_58376

2025-08-01 18:01:51,380 INFO Client connection 1754041985175_127.0.0.1_58376 disconnect, remove instances and subscribers

2025-08-01 18:02:04,072 INFO Client connection 1754042524046_127.0.0.1_59659 connect

2025-08-01 18:02:04,185 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=8}, 1754042524046_127.0.0.1_59659

2025-08-01 18:27:35,856 INFO Client connection 1754044055817_127.0.0.1_64395 connect

2025-08-01 18:27:35,965 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754044055817_127.0.0.1_64395

2025-08-01 18:31:13,632 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754044055817_127.0.0.1_64395

2025-08-01 18:31:13,642 INFO Client connection 1754044055817_127.0.0.1_64395 disconnect, remove instances and subscribers

2025-08-01 18:31:26,448 INFO Client connection 1754044286410_127.0.0.1_49392 connect

2025-08-01 18:31:26,560 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=2}, 1754044286410_127.0.0.1_49392

2025-08-01 18:34:10,166 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, 1754044286410_127.0.0.1_49392

2025-08-01 18:34:10,170 INFO Client connection 1754044286410_127.0.0.1_49392 disconnect, remove instances and subscribers

2025-08-01 18:34:26,549 INFO Client connection 1754044466511_127.0.0.1_50616 connect

2025-08-01 18:34:26,664 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=4}, 1754044466511_127.0.0.1_50616

2025-08-01 18:35:44,745 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, 1754044466511_127.0.0.1_50616

2025-08-01 18:35:44,749 INFO Client connection 1754044466511_127.0.0.1_50616 disconnect, remove instances and subscribers

2025-08-01 18:35:54,142 INFO Client connection 1754044554103_127.0.0.1_51169 connect

2025-08-01 18:35:54,257 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=6}, 1754044554103_127.0.0.1_51169

2025-08-01 18:45:02,550 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754034827382_127.0.0.1_59137

2025-08-01 18:45:02,556 INFO Client connection 1754034827382_127.0.0.1_59137 disconnect, remove instances and subscribers

2025-08-01 18:45:07,470 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, 1754042402201_127.0.0.1_59286

2025-08-01 18:45:07,494 INFO Client connection 1754042402201_127.0.0.1_59286 disconnect, remove instances and subscribers

2025-08-01 18:45:12,619 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, 1754035975042_127.0.0.1_61800

2025-08-01 18:45:12,624 INFO Client connection 1754035975042_127.0.0.1_61800 disconnect, remove instances and subscribers

2025-08-01 18:45:17,305 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, 1754038228119_127.0.0.1_50467

2025-08-01 18:45:17,311 INFO Client connection 1754038228119_127.0.0.1_50467 disconnect, remove instances and subscribers

2025-08-01 18:45:22,008 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, 1754042524046_127.0.0.1_59659

2025-08-01 18:45:22,011 INFO Client connection 1754042524046_127.0.0.1_59659 disconnect, remove instances and subscribers

2025-08-01 18:45:28,901 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}, 1754044554103_127.0.0.1_51169

2025-08-01 18:45:28,907 INFO Client connection 1754044554103_127.0.0.1_51169 disconnect, remove instances and subscribers

2025-08-01 18:46:02,859 INFO Client connection 1754045162818_127.0.0.1_54493 connect

2025-08-01 18:46:02,971 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=8}, 1754045162818_127.0.0.1_54493

2025-08-01 18:46:07,832 INFO Client connection 1754045167803_127.0.0.1_54551 connect

2025-08-01 18:46:07,947 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=4}, 1754045167803_127.0.0.1_54551

2025-08-01 18:46:11,988 INFO Client connection 1754045171960_127.0.0.1_54573 connect

2025-08-01 18:46:12,099 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, 1754045171960_127.0.0.1_54573

2025-08-01 18:46:18,607 INFO Client connection 1754045178577_127.0.0.1_54634 connect

2025-08-01 18:46:18,721 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=6}, 1754045178577_127.0.0.1_54634

2025-08-01 18:46:23,159 INFO Client connection 1754045183130_127.0.0.1_54661 connect

2025-08-01 18:46:23,272 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=8}, 1754045183130_127.0.0.1_54661

2025-08-01 18:46:29,836 INFO Client connection 1754045189810_127.0.0.1_54715 connect

2025-08-01 18:46:29,943 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, 1754045189810_127.0.0.1_54715

2025-08-01 18:52:39,056 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, 1754045171960_127.0.0.1_54573

2025-08-01 18:52:39,060 INFO Client connection 1754045171960_127.0.0.1_54573 disconnect, remove instances and subscribers

2025-08-01 18:53:51,980 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-01 18:57:29,887 INFO Client connection 1754045849857_127.0.0.1_58617 connect

2025-08-01 18:57:29,996 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754045849857_127.0.0.1_58617

2025-08-01 18:58:28,888 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754045849857_127.0.0.1_58617

2025-08-01 18:58:28,891 INFO Client connection 1754045849857_127.0.0.1_58617 disconnect, remove instances and subscribers

2025-08-01 18:58:49,678 INFO Client connection 1754045929650_127.0.0.1_58952 connect

2025-08-01 18:58:49,803 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754045929650_127.0.0.1_58952

2025-08-01 18:59:46,878 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754045929650_127.0.0.1_58952

2025-08-01 18:59:46,882 INFO Client connection 1754045929650_127.0.0.1_58952 disconnect, remove instances and subscribers

2025-08-01 19:00:52,006 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-01 19:47:56,506 INFO Client connection 1754048876477_127.0.0.1_57634 connect

2025-08-01 19:47:56,618 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754048876477_127.0.0.1_57634

2025-08-01 19:56:39,786 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, 1754048876477_127.0.0.1_57634

2025-08-01 19:56:39,792 INFO Client connection 1754048876477_127.0.0.1_57634 disconnect, remove instances and subscribers

2025-08-01 19:56:46,837 INFO Client connection 1754049406813_127.0.0.1_62007 connect

2025-08-01 19:56:46,950 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754049406813_127.0.0.1_62007

2025-08-01 20:09:09,392 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754049406813_127.0.0.1_62007

2025-08-01 20:09:09,396 INFO Client connection 1754049406813_127.0.0.1_62007 disconnect, remove instances and subscribers

2025-08-01 20:09:18,834 INFO Client connection 1754050158806_127.0.0.1_50255 connect

2025-08-01 20:09:18,943 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, 1754050158806_127.0.0.1_50255

2025-08-01 20:11:33,332 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, 1754050158806_127.0.0.1_50255

2025-08-01 20:11:33,335 INFO Client connection 1754050158806_127.0.0.1_50255 disconnect, remove instances and subscribers

2025-08-01 20:11:41,065 INFO Client connection 1754050301039_127.0.0.1_50779 connect

2025-08-01 20:11:41,175 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=6}, 1754050301039_127.0.0.1_50779

2025-08-01 20:26:33,093 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=7}, 1754050301039_127.0.0.1_50779

2025-08-01 20:26:33,097 INFO Client connection 1754050301039_127.0.0.1_50779 disconnect, remove instances and subscribers

2025-08-01 20:26:43,863 INFO Client connection 1754051203837_127.0.0.1_57380 connect

2025-08-01 20:26:43,975 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=8}, 1754051203837_127.0.0.1_57380

2025-08-01 21:06:42,266 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}, 1754045162818_127.0.0.1_54493

2025-08-01 21:06:42,276 INFO Client connection 1754045162818_127.0.0.1_54493 disconnect, remove instances and subscribers

2025-08-01 21:06:48,139 INFO Client connection 1754053608110_127.0.0.1_52026 connect

2025-08-01 21:06:48,250 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=10}, 1754053608110_127.0.0.1_52026

2025-08-01 21:07:54,728 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}, 1754053608110_127.0.0.1_52026

2025-08-01 21:07:54,734 INFO Client connection 1754053608110_127.0.0.1_52026 disconnect, remove instances and subscribers

2025-08-01 21:07:59,804 INFO Client connection 1754053679775_127.0.0.1_52405 connect

2025-08-01 21:07:59,917 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=12}, 1754053679775_127.0.0.1_52405

2025-08-01 21:14:19,239 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}, 1754053679775_127.0.0.1_52405

2025-08-01 21:14:19,247 INFO Client connection 1754053679775_127.0.0.1_52405 disconnect, remove instances and subscribers

2025-08-01 21:14:27,905 INFO Client connection 1754054067878_127.0.0.1_54668 connect

2025-08-01 21:14:28,017 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=14}, 1754054067878_127.0.0.1_54668

2025-08-01 21:19:09,625 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}, 1754054067878_127.0.0.1_54668

2025-08-01 21:19:09,633 INFO Client connection 1754054067878_127.0.0.1_54668 disconnect, remove instances and subscribers

2025-08-01 21:19:16,467 INFO Client connection 1754054356438_127.0.0.1_56302 connect

2025-08-01 21:19:16,579 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=16}, 1754054356438_127.0.0.1_56302

2025-08-01 21:22:26,942 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=17}, 1754054356438_127.0.0.1_56302

2025-08-01 21:22:26,948 INFO Client connection 1754054356438_127.0.0.1_56302 disconnect, remove instances and subscribers

2025-08-01 21:22:31,584 INFO Client connection 1754054551558_127.0.0.1_57347 connect

2025-08-01 21:22:31,697 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=18}, 1754054551558_127.0.0.1_57347

2025-08-01 21:25:45,410 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}, 1754054551558_127.0.0.1_57347

2025-08-01 21:25:45,417 INFO Client connection 1754054551558_127.0.0.1_57347 disconnect, remove instances and subscribers

2025-08-01 21:25:52,468 INFO Client connection 1754054752440_127.0.0.1_58449 connect

2025-08-01 21:25:52,575 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=20}, 1754054752440_127.0.0.1_58449

2025-08-01 21:26:17,775 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, 1754051203837_127.0.0.1_57380

2025-08-01 21:26:17,779 INFO Client connection 1754051203837_127.0.0.1_57380 disconnect, remove instances and subscribers

2025-08-01 21:26:17,787 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=21}, 1754054752440_127.0.0.1_58449

2025-08-01 21:26:17,794 INFO Client connection 1754054752440_127.0.0.1_58449 disconnect, remove instances and subscribers

2025-08-01 21:27:52,453 WARN namespace : public, [DEFAULT_GROUP@@gateway-service] services are automatically cleaned

2025-08-01 21:27:52,453 WARN namespace : public, [DEFAULT_GROUP@@course-service] services are automatically cleaned

2025-08-01 21:31:14,065 INFO Client connection 1754055074040_127.0.0.1_60346 connect

2025-08-01 21:31:14,178 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=0}, 1754055074040_127.0.0.1_60346

2025-08-01 21:31:53,209 INFO Client connection 1754055113185_127.0.0.1_60549 connect

2025-08-01 21:31:53,322 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=0}, 1754055113185_127.0.0.1_60549

2025-08-01 21:35:43,102 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, 1754055113185_127.0.0.1_60549

2025-08-01 21:35:43,109 INFO Client connection 1754055113185_127.0.0.1_60549 disconnect, remove instances and subscribers

2025-08-01 21:35:52,729 INFO Client connection 1754055352705_127.0.0.1_61849 connect

2025-08-01 21:35:52,841 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=2}, 1754055352705_127.0.0.1_61849

2025-08-01 21:38:01,765 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, 1754055352705_127.0.0.1_61849

2025-08-01 21:38:01,773 INFO Client connection 1754055352705_127.0.0.1_61849 disconnect, remove instances and subscribers

2025-08-01 21:38:08,496 INFO Client connection 1754055488472_127.0.0.1_62597 connect

2025-08-01 21:38:08,606 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=4}, 1754055488472_127.0.0.1_62597

2025-08-01 21:39:46,028 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, 1754055488472_127.0.0.1_62597

2025-08-01 21:39:46,033 INFO Client connection 1754055488472_127.0.0.1_62597 disconnect, remove instances and subscribers

2025-08-01 21:39:53,572 INFO Client connection 1754055593548_127.0.0.1_63204 connect

2025-08-01 21:39:53,685 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=6}, 1754055593548_127.0.0.1_63204

2025-08-01 21:42:32,099 INFO Client connection 1754045167803_127.0.0.1_54551 disconnect, remove instances and subscribers

2025-08-01 21:42:39,452 INFO Client connection 1754055759425_127.0.0.1_64131 connect

2025-08-01 21:42:39,565 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=6}, 1754055759425_127.0.0.1_64131

2025-08-01 21:47:34,701 INFO Client connection 1754045183130_127.0.0.1_54661 disconnect, remove instances and subscribers

2025-08-01 21:47:42,873 INFO Client connection 1754056062849_127.0.0.1_49413 connect

2025-08-01 21:47:42,986 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=10}, 1754056062849_127.0.0.1_49413

2025-08-01 21:49:17,542 INFO Client connection 1754055074040_127.0.0.1_60346 disconnect, remove instances and subscribers

2025-08-01 21:49:17,544 INFO Client connection 1754055593548_127.0.0.1_63204 disconnect, remove instances and subscribers

2025-08-01 21:49:26,484 INFO Client connection 1754056166460_127.0.0.1_50026 connect

2025-08-01 21:49:26,597 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, 1754056166460_127.0.0.1_50026

2025-08-01 21:50:22,865 INFO Client connection 1754056222838_127.0.0.1_50335 connect

2025-08-01 21:50:22,978 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=8}, 1754056222838_127.0.0.1_50335

2025-08-01 21:55:53,496 INFO Client connection 1754045178577_127.0.0.1_54634 disconnect, remove instances and subscribers

2025-08-01 21:56:02,596 INFO Client connection 1754056562568_127.0.0.1_52188 connect

2025-08-01 21:56:02,708 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=8}, 1754056562568_127.0.0.1_52188

2025-08-01 21:59:19,960 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=9}, 1754056562568_127.0.0.1_52188

2025-08-01 21:59:19,964 INFO Client connection 1754056562568_127.0.0.1_52188 disconnect, remove instances and subscribers

2025-08-01 21:59:46,187 INFO Client connection 1754056786155_127.0.0.1_53463 connect

2025-08-01 21:59:46,296 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=10}, 1754056786155_127.0.0.1_53463

2025-08-01 22:02:37,271 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=11}, 1754056786155_127.0.0.1_53463

2025-08-01 22:02:37,276 INFO Client connection 1754056786155_127.0.0.1_53463 disconnect, remove instances and subscribers

2025-08-01 22:02:47,709 INFO Client connection 1754056967683_127.0.0.1_54551 connect

2025-08-01 22:02:47,822 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=12}, 1754056967683_127.0.0.1_54551

2025-08-01 22:03:59,212 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=13}, 1754056967683_127.0.0.1_54551

2025-08-01 22:03:59,215 INFO Client connection 1754056967683_127.0.0.1_54551 disconnect, remove instances and subscribers

2025-08-01 22:04:55,560 INFO Client connection 1754057095532_127.0.0.1_55329 connect

2025-08-01 22:04:55,673 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=14}, 1754057095532_127.0.0.1_55329

2025-08-01 22:08:30,672 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}, 1754056222838_127.0.0.1_50335

2025-08-01 22:08:30,678 INFO Client connection 1754056222838_127.0.0.1_50335 disconnect, remove instances and subscribers

2025-08-01 22:08:36,485 INFO Client connection 1754057316460_127.0.0.1_56565 connect

2025-08-01 22:08:36,593 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=10}, 1754057316460_127.0.0.1_56565

2025-08-01 22:11:13,115 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}, 1754057316460_127.0.0.1_56565

2025-08-01 22:11:13,122 INFO Client connection 1754057316460_127.0.0.1_56565 disconnect, remove instances and subscribers

2025-08-01 22:11:20,475 INFO Client connection 1754057480448_127.0.0.1_57561 connect

2025-08-01 22:11:20,585 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=12}, 1754057480448_127.0.0.1_57561

2025-08-01 22:13:03,179 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}, 1754057480448_127.0.0.1_57561

2025-08-01 22:13:03,186 INFO Client connection 1754057480448_127.0.0.1_57561 disconnect, remove instances and subscribers

2025-08-01 22:13:10,805 INFO Client connection 1754057590780_127.0.0.1_58201 connect

2025-08-01 22:13:10,918 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=14}, 1754057590780_127.0.0.1_58201

2025-08-01 22:14:56,645 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}, 1754057590780_127.0.0.1_58201

2025-08-01 22:14:56,652 INFO Client connection 1754057590780_127.0.0.1_58201 disconnect, remove instances and subscribers

2025-08-01 22:15:10,681 INFO Client connection 1754057710652_127.0.0.1_58891 connect

2025-08-01 22:15:10,792 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=16}, 1754057710652_127.0.0.1_58891

2025-08-01 22:19:45,873 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, 1754056166460_127.0.0.1_50026

2025-08-01 22:19:45,877 INFO Client connection 1754056166460_127.0.0.1_50026 disconnect, remove instances and subscribers

2025-08-01 22:19:55,394 INFO Client connection 1754057995367_127.0.0.1_60561 connect

2025-08-01 22:19:55,505 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, 1754057995367_127.0.0.1_60561

2025-08-01 22:21:42,590 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, 1754057995367_127.0.0.1_60561

2025-08-01 22:21:42,593 INFO Client connection 1754057995367_127.0.0.1_60561 disconnect, remove instances and subscribers

2025-08-01 22:21:51,803 INFO Client connection 1754058111779_127.0.0.1_61205 connect

2025-08-01 22:21:51,911 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=6}, 1754058111779_127.0.0.1_61205

2025-08-01 22:34:14,709 INFO Client connection 1754057095532_127.0.0.1_55329 disconnect, remove instances and subscribers

2025-08-01 22:34:23,033 INFO Client connection 1754058863006_127.0.0.1_63254 connect

2025-08-01 22:34:23,145 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=16}, 1754058863006_127.0.0.1_63254

2025-08-01 22:42:19,396 INFO Client connection 1754058111779_127.0.0.1_61205 disconnect, remove instances and subscribers

2025-08-01 22:42:27,591 INFO Client connection 1754059347565_127.0.0.1_65480 connect

2025-08-01 22:42:27,703 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=8}, 1754059347565_127.0.0.1_65480

2025-08-01 22:46:58,758 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, 1754059347565_127.0.0.1_65480

2025-08-01 22:46:58,761 INFO Client connection 1754059347565_127.0.0.1_65480 disconnect, remove instances and subscribers

2025-08-01 22:47:25,620 INFO Client connection 1754059645594_127.0.0.1_50692 connect

2025-08-01 22:47:25,731 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=10}, 1754059645594_127.0.0.1_50692

2025-08-01 23:33:07,016 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=11}, 1754059645594_127.0.0.1_50692

2025-08-01 23:33:07,020 INFO Client connection 1754059645594_127.0.0.1_50692 disconnect, remove instances and subscribers

2025-08-01 23:33:14,745 INFO Client connection 1754062394721_127.0.0.1_59464 connect

2025-08-01 23:33:14,859 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=12}, 1754062394721_127.0.0.1_59464

2025-08-01 23:34:26,824 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, 1754056062849_127.0.0.1_49413

2025-08-01 23:34:26,828 INFO Client connection 1754056062849_127.0.0.1_49413 disconnect, remove instances and subscribers

2025-08-01 23:34:35,354 INFO Client connection 1754062475327_127.0.0.1_59592 connect

2025-08-01 23:34:35,466 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=12}, 1754062475327_127.0.0.1_59592

2025-08-01 23:39:20,056 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, 1754045189810_127.0.0.1_54715

2025-08-01 23:39:20,063 INFO Client connection 1754045189810_127.0.0.1_54715 disconnect, remove instances and subscribers

2025-08-01 23:40:53,021 WARN namespace : public, [DEFAULT_GROUP@@discussion-service] services are automatically cleaned

2025-08-01 23:44:14,239 INFO Client connection 1754063054215_127.0.0.1_60326 connect

2025-08-01 23:44:14,354 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=0}, 1754063054215_127.0.0.1_60326

2025-08-01 23:45:22,840 INFO Client remove for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, 1754063054215_127.0.0.1_60326

2025-08-01 23:45:22,844 INFO Client connection 1754063054215_127.0.0.1_60326 disconnect, remove instances and subscribers

2025-08-01 23:45:29,093 INFO Client connection 1754063129068_127.0.0.1_60434 connect

2025-08-01 23:45:29,202 INFO Client change for service Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, 1754063129068_127.0.0.1_60434

