[2025-08-02T00:29:36.447+0800][gc,init] CardTable entry size: 512
[2025-08-02T00:29:36.448+0800][gc     ] Using G1
[2025-08-02T00:29:36.449+0800][gc,init] Version: 23.0.2 (release)
[2025-08-02T00:29:36.449+0800][gc,init] CPUs: 12 total, 12 available
[2025-08-02T00:29:36.449+0800][gc,init] Memory: 49152M
[2025-08-02T00:29:36.449+0800][gc,init] Large Page Support: Disabled
[2025-08-02T00:29:36.449+0800][gc,init] NUMA Support: Disabled
[2025-08-02T00:29:36.449+0800][gc,init] Compressed Oops: Enabled (Zero based)
[2025-08-02T00:29:36.449+0800][gc,init] Heap Region Size: 1M
[2025-08-02T00:29:36.449+0800][gc,init] Heap Min Capacity: 512M
[2025-08-02T00:29:36.449+0800][gc,init] Heap Initial Capacity: 512M
[2025-08-02T00:29:36.449+0800][gc,init] Heap Max Capacity: 512M
[2025-08-02T00:29:36.449+0800][gc,init] Pre-touch: Disabled
[2025-08-02T00:29:36.449+0800][gc,init] Parallel Workers: 10
[2025-08-02T00:29:36.449+0800][gc,init] Concurrent Workers: 3
[2025-08-02T00:29:36.449+0800][gc,init] Concurrent Refinement Workers: 10
[2025-08-02T00:29:36.449+0800][gc,init] Periodic GC: Disabled
[2025-08-02T00:29:36.454+0800][gc,metaspace] CDS archive(s) mapped at: [0x000003ff00000000-0x000003ff00d94000-0x000003ff00d94000), size 14237696, SharedBaseAddress: 0x000003ff00000000, ArchiveRelocationMode: 1.
[2025-08-02T00:29:36.454+0800][gc,metaspace] Compressed class space mapped at: 0x000003ff01000000-0x000003ff11000000, reserved size: 268435456
[2025-08-02T00:29:36.454+0800][gc,metaspace] Narrow klass base: 0x000003ff00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-08-02T00:29:36.839+0800][gc,start    ] GC(0) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:36.840+0800][gc,task     ] GC(0) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:36.842+0800][gc,phases   ] GC(0)   Pre Evacuate Collection Set: 0.10ms
[2025-08-02T00:29:36.842+0800][gc,phases   ] GC(0)   Merge Heap Roots: 0.10ms
[2025-08-02T00:29:36.842+0800][gc,phases   ] GC(0)   Evacuate Collection Set: 1.47ms
[2025-08-02T00:29:36.842+0800][gc,phases   ] GC(0)   Post Evacuate Collection Set: 0.28ms
[2025-08-02T00:29:36.842+0800][gc,phases   ] GC(0)   Other: 0.25ms
[2025-08-02T00:29:36.842+0800][gc,heap     ] GC(0) Eden regions: 256->0(251)
[2025-08-02T00:29:36.842+0800][gc,heap     ] GC(0) Survivor regions: 0->5(32)
[2025-08-02T00:29:36.842+0800][gc,heap     ] GC(0) Old regions: 2->2
[2025-08-02T00:29:36.842+0800][gc,heap     ] GC(0) Humongous regions: 3->0
[2025-08-02T00:29:36.842+0800][gc,metaspace] GC(0) Metaspace: 8478K(8768K)->8478K(8768K) NonClass: 7470K(7616K)->7470K(7616K) Class: 1008K(1152K)->1008K(1152K)
[2025-08-02T00:29:36.842+0800][gc          ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 260M->5M(512M) 2.353ms
[2025-08-02T00:29:36.842+0800][gc,cpu      ] GC(0) User=0.01s Sys=0.00s Real=0.01s
[2025-08-02T00:29:37.030+0800][gc,start    ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.030+0800][gc,task     ] GC(1) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.032+0800][gc,phases   ] GC(1)   Pre Evacuate Collection Set: 0.12ms
[2025-08-02T00:29:37.032+0800][gc,phases   ] GC(1)   Merge Heap Roots: 0.10ms
[2025-08-02T00:29:37.032+0800][gc,phases   ] GC(1)   Evacuate Collection Set: 1.27ms
[2025-08-02T00:29:37.032+0800][gc,phases   ] GC(1)   Post Evacuate Collection Set: 0.26ms
[2025-08-02T00:29:37.032+0800][gc,phases   ] GC(1)   Other: 0.04ms
[2025-08-02T00:29:37.032+0800][gc,heap     ] GC(1) Eden regions: 251->0(248)
[2025-08-02T00:29:37.032+0800][gc,heap     ] GC(1) Survivor regions: 5->8(32)
[2025-08-02T00:29:37.032+0800][gc,heap     ] GC(1) Old regions: 2->2
[2025-08-02T00:29:37.032+0800][gc,heap     ] GC(1) Humongous regions: 0->0
[2025-08-02T00:29:37.032+0800][gc,metaspace] GC(1) Metaspace: 14649K(14912K)->14649K(14912K) NonClass: 12930K(13056K)->12930K(13056K) Class: 1719K(1856K)->1719K(1856K)
[2025-08-02T00:29:37.032+0800][gc          ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 256M->8M(512M) 1.906ms
[2025-08-02T00:29:37.032+0800][gc,cpu      ] GC(1) User=0.01s Sys=0.00s Real=0.00s
[2025-08-02T00:29:37.257+0800][gc,start    ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.257+0800][gc,task     ] GC(2) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.260+0800][gc,phases   ] GC(2)   Pre Evacuate Collection Set: 0.13ms
[2025-08-02T00:29:37.260+0800][gc,phases   ] GC(2)   Merge Heap Roots: 0.08ms
[2025-08-02T00:29:37.260+0800][gc,phases   ] GC(2)   Evacuate Collection Set: 2.04ms
[2025-08-02T00:29:37.260+0800][gc,phases   ] GC(2)   Post Evacuate Collection Set: 0.39ms
[2025-08-02T00:29:37.260+0800][gc,phases   ] GC(2)   Other: 0.04ms
[2025-08-02T00:29:37.260+0800][gc,heap     ] GC(2) Eden regions: 248->0(241)
[2025-08-02T00:29:37.260+0800][gc,heap     ] GC(2) Survivor regions: 8->15(32)
[2025-08-02T00:29:37.260+0800][gc,heap     ] GC(2) Old regions: 2->2
[2025-08-02T00:29:37.260+0800][gc,heap     ] GC(2) Humongous regions: 0->0
[2025-08-02T00:29:37.260+0800][gc,metaspace] GC(2) Metaspace: 19069K(19328K)->19069K(19328K) NonClass: 16772K(16896K)->16772K(16896K) Class: 2296K(2432K)->2296K(2432K)
[2025-08-02T00:29:37.260+0800][gc          ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 256M->15M(512M) 2.802ms
[2025-08-02T00:29:37.260+0800][gc,cpu      ] GC(2) User=0.02s Sys=0.01s Real=0.01s
[2025-08-02T00:29:37.429+0800][gc,start    ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.429+0800][gc,task     ] GC(3) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.432+0800][gc,phases   ] GC(3)   Pre Evacuate Collection Set: 0.10ms
[2025-08-02T00:29:37.432+0800][gc,phases   ] GC(3)   Merge Heap Roots: 0.09ms
[2025-08-02T00:29:37.432+0800][gc,phases   ] GC(3)   Evacuate Collection Set: 2.52ms
[2025-08-02T00:29:37.432+0800][gc,phases   ] GC(3)   Post Evacuate Collection Set: 0.45ms
[2025-08-02T00:29:37.432+0800][gc,phases   ] GC(3)   Other: 0.04ms
[2025-08-02T00:29:37.432+0800][gc,heap     ] GC(3) Eden regions: 241->0(237)
[2025-08-02T00:29:37.432+0800][gc,heap     ] GC(3) Survivor regions: 15->19(32)
[2025-08-02T00:29:37.432+0800][gc,heap     ] GC(3) Old regions: 2->2
[2025-08-02T00:29:37.432+0800][gc,heap     ] GC(3) Humongous regions: 0->0
[2025-08-02T00:29:37.432+0800][gc,metaspace] GC(3) Metaspace: 19897K(20224K)->19897K(20224K) NonClass: 17511K(17664K)->17511K(17664K) Class: 2386K(2560K)->2386K(2560K)
[2025-08-02T00:29:37.432+0800][gc          ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 256M->19M(512M) 3.314ms
[2025-08-02T00:29:37.432+0800][gc,cpu      ] GC(3) User=0.02s Sys=0.00s Real=0.01s
[2025-08-02T00:29:37.552+0800][gc,start    ] GC(4) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.552+0800][gc,task     ] GC(4) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.555+0800][gc,phases   ] GC(4)   Pre Evacuate Collection Set: 0.12ms
[2025-08-02T00:29:37.555+0800][gc,phases   ] GC(4)   Merge Heap Roots: 0.07ms
[2025-08-02T00:29:37.555+0800][gc,phases   ] GC(4)   Evacuate Collection Set: 1.91ms
[2025-08-02T00:29:37.555+0800][gc,phases   ] GC(4)   Post Evacuate Collection Set: 0.46ms
[2025-08-02T00:29:37.555+0800][gc,phases   ] GC(4)   Other: 0.04ms
[2025-08-02T00:29:37.555+0800][gc,heap     ] GC(4) Eden regions: 237->0(239)
[2025-08-02T00:29:37.555+0800][gc,heap     ] GC(4) Survivor regions: 19->17(32)
[2025-08-02T00:29:37.555+0800][gc,heap     ] GC(4) Old regions: 2->7
[2025-08-02T00:29:37.555+0800][gc,heap     ] GC(4) Humongous regions: 0->0
[2025-08-02T00:29:37.555+0800][gc,metaspace] GC(4) Metaspace: 22142K(22464K)->22142K(22464K) NonClass: 19498K(19648K)->19498K(19648K) Class: 2643K(2816K)->2643K(2816K)
[2025-08-02T00:29:37.555+0800][gc          ] GC(4) Pause Young (Normal) (G1 Evacuation Pause) 256M->21M(512M) 2.696ms
[2025-08-02T00:29:37.555+0800][gc,cpu      ] GC(4) User=0.02s Sys=0.00s Real=0.00s
[2025-08-02T00:29:37.744+0800][gc,start    ] GC(5) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.744+0800][gc,task     ] GC(5) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.746+0800][gc,phases   ] GC(5)   Pre Evacuate Collection Set: 0.15ms
[2025-08-02T00:29:37.746+0800][gc,phases   ] GC(5)   Merge Heap Roots: 0.07ms
[2025-08-02T00:29:37.746+0800][gc,phases   ] GC(5)   Evacuate Collection Set: 1.45ms
[2025-08-02T00:29:37.746+0800][gc,phases   ] GC(5)   Post Evacuate Collection Set: 0.28ms
[2025-08-02T00:29:37.746+0800][gc,phases   ] GC(5)   Other: 0.04ms
[2025-08-02T00:29:37.746+0800][gc,heap     ] GC(5) Eden regions: 239->0(239)
[2025-08-02T00:29:37.746+0800][gc,heap     ] GC(5) Survivor regions: 17->17(32)
[2025-08-02T00:29:37.746+0800][gc,heap     ] GC(5) Old regions: 7->7
[2025-08-02T00:29:37.746+0800][gc,heap     ] GC(5) Humongous regions: 0->0
[2025-08-02T00:29:37.746+0800][gc,metaspace] GC(5) Metaspace: 24582K(24896K)->24582K(24896K) NonClass: 21602K(21760K)->21602K(21760K) Class: 2979K(3136K)->2979K(3136K)
[2025-08-02T00:29:37.746+0800][gc          ] GC(5) Pause Young (Normal) (G1 Evacuation Pause) 260M->22M(512M) 2.110ms
[2025-08-02T00:29:37.746+0800][gc,cpu      ] GC(5) User=0.01s Sys=0.00s Real=0.00s
[2025-08-02T00:29:37.964+0800][gc,start    ] GC(6) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:37.964+0800][gc,task     ] GC(6) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:37.966+0800][gc,phases   ] GC(6)   Pre Evacuate Collection Set: 0.11ms
[2025-08-02T00:29:37.966+0800][gc,phases   ] GC(6)   Merge Heap Roots: 0.07ms
[2025-08-02T00:29:37.966+0800][gc,phases   ] GC(6)   Evacuate Collection Set: 1.72ms
[2025-08-02T00:29:37.966+0800][gc,phases   ] GC(6)   Post Evacuate Collection Set: 0.32ms
[2025-08-02T00:29:37.966+0800][gc,phases   ] GC(6)   Other: 0.04ms
[2025-08-02T00:29:37.966+0800][gc,heap     ] GC(6) Eden regions: 239->0(237)
[2025-08-02T00:29:37.966+0800][gc,heap     ] GC(6) Survivor regions: 17->19(32)
[2025-08-02T00:29:37.966+0800][gc,heap     ] GC(6) Old regions: 7->7
[2025-08-02T00:29:37.966+0800][gc,heap     ] GC(6) Humongous regions: 0->0
[2025-08-02T00:29:37.966+0800][gc,metaspace] GC(6) Metaspace: 28010K(28288K)->28010K(28288K) NonClass: 24574K(24704K)->24574K(24704K) Class: 3436K(3584K)->3436K(3584K)
[2025-08-02T00:29:37.966+0800][gc          ] GC(6) Pause Young (Normal) (G1 Evacuation Pause) 261M->24M(512M) 2.363ms
[2025-08-02T00:29:37.966+0800][gc,cpu      ] GC(6) User=0.02s Sys=0.00s Real=0.00s
[2025-08-02T00:29:38.305+0800][gc,start    ] GC(7) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:38.305+0800][gc,task     ] GC(7) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:38.309+0800][gc,phases   ] GC(7)   Pre Evacuate Collection Set: 0.13ms
[2025-08-02T00:29:38.309+0800][gc,phases   ] GC(7)   Merge Heap Roots: 0.07ms
[2025-08-02T00:29:38.309+0800][gc,phases   ] GC(7)   Evacuate Collection Set: 2.54ms
[2025-08-02T00:29:38.309+0800][gc,phases   ] GC(7)   Post Evacuate Collection Set: 0.34ms
[2025-08-02T00:29:38.309+0800][gc,phases   ] GC(7)   Other: 0.05ms
[2025-08-02T00:29:38.309+0800][gc,heap     ] GC(7) Eden regions: 237->0(235)
[2025-08-02T00:29:38.309+0800][gc,heap     ] GC(7) Survivor regions: 19->21(32)
[2025-08-02T00:29:38.309+0800][gc,heap     ] GC(7) Old regions: 7->9
[2025-08-02T00:29:38.309+0800][gc,heap     ] GC(7) Humongous regions: 0->0
[2025-08-02T00:29:38.309+0800][gc,metaspace] GC(7) Metaspace: 36437K(36864K)->36437K(36864K) NonClass: 31974K(32192K)->31974K(32192K) Class: 4463K(4672K)->4463K(4672K)
[2025-08-02T00:29:38.309+0800][gc          ] GC(7) Pause Young (Normal) (G1 Evacuation Pause) 261M->28M(512M) 3.245ms
[2025-08-02T00:29:38.309+0800][gc,cpu      ] GC(7) User=0.03s Sys=0.01s Real=0.00s
[2025-08-02T00:29:38.685+0800][gc,start    ] GC(8) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:38.685+0800][gc,task     ] GC(8) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:38.689+0800][gc,phases   ] GC(8)   Pre Evacuate Collection Set: 0.15ms
[2025-08-02T00:29:38.689+0800][gc,phases   ] GC(8)   Merge Heap Roots: 0.08ms
[2025-08-02T00:29:38.689+0800][gc,phases   ] GC(8)   Evacuate Collection Set: 3.21ms
[2025-08-02T00:29:38.689+0800][gc,phases   ] GC(8)   Post Evacuate Collection Set: 0.45ms
[2025-08-02T00:29:38.689+0800][gc,phases   ] GC(8)   Other: 0.05ms
[2025-08-02T00:29:38.689+0800][gc,heap     ] GC(8) Eden regions: 235->0(236)
[2025-08-02T00:29:38.690+0800][gc,heap     ] GC(8) Survivor regions: 21->20(32)
[2025-08-02T00:29:38.690+0800][gc,heap     ] GC(8) Old regions: 9->16
[2025-08-02T00:29:38.690+0800][gc,heap     ] GC(8) Humongous regions: 0->0
[2025-08-02T00:29:38.690+0800][gc,metaspace] GC(8) Metaspace: 44934K(45376K)->44934K(45376K) NonClass: 39407K(39680K)->39407K(39680K) Class: 5526K(5696K)->5526K(5696K)
[2025-08-02T00:29:38.690+0800][gc          ] GC(8) Pause Young (Normal) (G1 Evacuation Pause) 263M->34M(512M) 4.070ms
[2025-08-02T00:29:38.690+0800][gc,cpu      ] GC(8) User=0.03s Sys=0.00s Real=0.01s
[2025-08-02T00:29:39.002+0800][gc,start    ] GC(9) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:39.002+0800][gc,task     ] GC(9) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:39.006+0800][gc,phases   ] GC(9)   Pre Evacuate Collection Set: 0.10ms
[2025-08-02T00:29:39.006+0800][gc,phases   ] GC(9)   Merge Heap Roots: 0.10ms
[2025-08-02T00:29:39.006+0800][gc,phases   ] GC(9)   Evacuate Collection Set: 3.18ms
[2025-08-02T00:29:39.006+0800][gc,phases   ] GC(9)   Post Evacuate Collection Set: 0.50ms
[2025-08-02T00:29:39.006+0800][gc,phases   ] GC(9)   Other: 0.04ms
[2025-08-02T00:29:39.006+0800][gc,heap     ] GC(9) Eden regions: 236->0(234)
[2025-08-02T00:29:39.006+0800][gc,heap     ] GC(9) Survivor regions: 20->22(32)
[2025-08-02T00:29:39.006+0800][gc,heap     ] GC(9) Old regions: 16->21
[2025-08-02T00:29:39.006+0800][gc,heap     ] GC(9) Humongous regions: 0->0
[2025-08-02T00:29:39.006+0800][gc,metaspace] GC(9) Metaspace: 51410K(51904K)->51410K(51904K) NonClass: 45047K(45312K)->45047K(45312K) Class: 6362K(6592K)->6362K(6592K)
[2025-08-02T00:29:39.006+0800][gc          ] GC(9) Pause Young (Normal) (G1 Evacuation Pause) 270M->41M(512M) 4.025ms
[2025-08-02T00:29:39.006+0800][gc,cpu      ] GC(9) User=0.04s Sys=0.00s Real=0.00s
[2025-08-02T00:29:39.195+0800][gc,start    ] GC(10) Pause Young (Normal) (G1 Evacuation Pause)
[2025-08-02T00:29:39.195+0800][gc,task     ] GC(10) Using 10 workers of 10 for evacuation
[2025-08-02T00:29:39.198+0800][gc,phases   ] GC(10)   Pre Evacuate Collection Set: 0.11ms
[2025-08-02T00:29:39.198+0800][gc,phases   ] GC(10)   Merge Heap Roots: 0.07ms
[2025-08-02T00:29:39.198+0800][gc,phases   ] GC(10)   Evacuate Collection Set: 3.05ms
[2025-08-02T00:29:39.198+0800][gc,phases   ] GC(10)   Post Evacuate Collection Set: 0.36ms
[2025-08-02T00:29:39.198+0800][gc,phases   ] GC(10)   Other: 0.04ms
[2025-08-02T00:29:39.198+0800][gc,heap     ] GC(10) Eden regions: 234->0(237)
[2025-08-02T00:29:39.198+0800][gc,heap     ] GC(10) Survivor regions: 22->19(32)
[2025-08-02T00:29:39.198+0800][gc,heap     ] GC(10) Old regions: 21->25
[2025-08-02T00:29:39.198+0800][gc,heap     ] GC(10) Humongous regions: 0->0
[2025-08-02T00:29:39.198+0800][gc,metaspace] GC(10) Metaspace: 57219K(57728K)->57219K(57728K) NonClass: 50114K(50368K)->50114K(50368K) Class: 7104K(7360K)->7104K(7360K)
[2025-08-02T00:29:39.198+0800][gc          ] GC(10) Pause Young (Normal) (G1 Evacuation Pause) 275M->43M(512M) 3.734ms
[2025-08-02T00:29:39.198+0800][gc,cpu      ] GC(10) User=0.03s Sys=0.00s Real=0.00s
[2025-08-02T00:29:39.200+0800][gc,heap,exit] Heap
[2025-08-02T00:29:39.200+0800][gc,heap,exit]  garbage-first heap   total reserved 524288K, committed 524288K, used 50435K [0x00000007e0000000, 0x0000000800000000)
[2025-08-02T00:29:39.200+0800][gc,heap,exit]   region size 1024K, 26 young (26624K), 19 survivors (19456K)
[2025-08-02T00:29:39.200+0800][gc,heap,exit]  Metaspace       used 57234K, committed 57792K, reserved 327680K
[2025-08-02T00:29:39.200+0800][gc,heap,exit]   class space    used 7106K, committed 7360K, reserved 262144K
