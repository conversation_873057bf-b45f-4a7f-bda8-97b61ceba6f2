2025-08-02 00:00:00,387 INFO Connection check task start

2025-08-02 00:00:00,390 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:00,390 INFO Out dated connection ,size=0

2025-08-02 00:00:00,390 INFO Connection check task end

2025-08-02 00:00:01,577 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:03,395 INFO Connection check task start

2025-08-02 00:00:03,396 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:03,396 INFO Out dated connection ,size=0

2025-08-02 00:00:03,396 INFO Connection check task end

2025-08-02 00:00:04,580 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:06,398 INFO Connection check task start

2025-08-02 00:00:06,398 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:06,399 INFO Out dated connection ,size=0

2025-08-02 00:00:06,399 INFO Connection check task end

2025-08-02 00:00:07,585 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:09,400 INFO Connection check task start

2025-08-02 00:00:09,400 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:09,400 INFO Out dated connection ,size=0

2025-08-02 00:00:09,400 INFO Connection check task end

2025-08-02 00:00:10,586 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:12,402 INFO Connection check task start

2025-08-02 00:00:12,402 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:12,402 INFO Out dated connection ,size=0

2025-08-02 00:00:12,402 INFO Connection check task end

2025-08-02 00:00:13,589 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:15,404 INFO Connection check task start

2025-08-02 00:00:15,404 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:15,404 INFO Out dated connection ,size=0

2025-08-02 00:00:15,404 INFO Connection check task end

2025-08-02 00:00:16,596 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:18,408 INFO Connection check task start

2025-08-02 00:00:18,408 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:18,408 INFO Out dated connection ,size=0

2025-08-02 00:00:18,408 INFO Connection check task end

2025-08-02 00:00:19,598 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:21,413 INFO Connection check task start

2025-08-02 00:00:21,413 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:21,413 INFO Out dated connection ,size=0

2025-08-02 00:00:21,413 INFO Connection check task end

2025-08-02 00:00:22,600 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:24,416 INFO Connection check task start

2025-08-02 00:00:24,417 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:24,417 INFO Out dated connection ,size=0

2025-08-02 00:00:24,417 INFO Connection check task end

2025-08-02 00:00:25,602 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:27,421 INFO Connection check task start

2025-08-02 00:00:27,421 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:27,421 INFO Out dated connection ,size=0

2025-08-02 00:00:27,421 INFO Connection check task end

2025-08-02 00:00:28,603 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:30,424 INFO Connection check task start

2025-08-02 00:00:30,426 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:30,426 INFO Out dated connection ,size=0

2025-08-02 00:00:30,426 INFO Connection check task end

2025-08-02 00:00:31,608 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:33,432 INFO Connection check task start

2025-08-02 00:00:33,432 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:33,433 INFO Out dated connection ,size=0

2025-08-02 00:00:33,433 INFO Connection check task end

2025-08-02 00:00:34,614 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:36,438 INFO Connection check task start

2025-08-02 00:00:36,438 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:36,438 INFO Out dated connection ,size=0

2025-08-02 00:00:36,438 INFO Connection check task end

2025-08-02 00:00:37,615 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:39,442 INFO Connection check task start

2025-08-02 00:00:39,443 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:39,443 INFO Out dated connection ,size=0

2025-08-02 00:00:39,443 INFO Connection check task end

2025-08-02 00:00:40,619 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:42,445 INFO Connection check task start

2025-08-02 00:00:42,445 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:42,445 INFO Out dated connection ,size=0

2025-08-02 00:00:42,445 INFO Connection check task end

2025-08-02 00:00:43,625 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:45,450 INFO Connection check task start

2025-08-02 00:00:45,451 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:45,451 INFO Out dated connection ,size=0

2025-08-02 00:00:45,451 INFO Connection check task end

2025-08-02 00:00:46,626 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:48,452 INFO Connection check task start

2025-08-02 00:00:48,452 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:48,452 INFO Out dated connection ,size=0

2025-08-02 00:00:48,453 INFO Connection check task end

2025-08-02 00:00:49,632 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:51,453 INFO Connection check task start

2025-08-02 00:00:51,453 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:51,453 INFO Out dated connection ,size=0

2025-08-02 00:00:51,453 INFO Connection check task end

2025-08-02 00:00:52,635 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:54,458 INFO Connection check task start

2025-08-02 00:00:54,459 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:54,459 INFO Out dated connection ,size=0

2025-08-02 00:00:54,459 INFO Connection check task end

2025-08-02 00:00:55,640 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:00:57,462 INFO Connection check task start

2025-08-02 00:00:57,462 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:00:57,462 INFO Out dated connection ,size=0

2025-08-02 00:00:57,462 INFO Connection check task end

2025-08-02 00:00:58,642 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:00,464 INFO Connection check task start

2025-08-02 00:01:00,464 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:00,464 INFO Out dated connection ,size=0

2025-08-02 00:01:00,464 INFO Connection check task end

2025-08-02 00:01:01,648 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:03,469 INFO Connection check task start

2025-08-02 00:01:03,470 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:03,470 INFO Out dated connection ,size=0

2025-08-02 00:01:03,470 INFO Connection check task end

2025-08-02 00:01:04,654 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:06,474 INFO Connection check task start

2025-08-02 00:01:06,474 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:06,475 INFO Out dated connection ,size=0

2025-08-02 00:01:06,475 INFO Connection check task end

2025-08-02 00:01:07,658 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:09,476 INFO Connection check task start

2025-08-02 00:01:09,476 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:09,476 INFO Out dated connection ,size=0

2025-08-02 00:01:09,476 INFO Connection check task end

2025-08-02 00:01:10,663 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:12,480 INFO Connection check task start

2025-08-02 00:01:12,480 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:12,480 INFO Out dated connection ,size=0

2025-08-02 00:01:12,480 INFO Connection check task end

2025-08-02 00:01:13,666 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:15,485 INFO Connection check task start

2025-08-02 00:01:15,486 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:15,486 INFO Out dated connection ,size=0

2025-08-02 00:01:15,486 INFO Connection check task end

2025-08-02 00:01:16,669 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:18,490 INFO Connection check task start

2025-08-02 00:01:18,490 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:18,490 INFO Out dated connection ,size=0

2025-08-02 00:01:18,490 INFO Connection check task end

2025-08-02 00:01:19,671 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:21,491 INFO Connection check task start

2025-08-02 00:01:21,492 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:21,492 INFO Out dated connection ,size=0

2025-08-02 00:01:21,492 INFO Connection check task end

2025-08-02 00:01:22,674 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:24,494 INFO Connection check task start

2025-08-02 00:01:24,494 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:24,495 INFO Out dated connection ,size=0

2025-08-02 00:01:24,495 INFO Connection check task end

2025-08-02 00:01:25,674 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:27,495 INFO Connection check task start

2025-08-02 00:01:27,496 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:27,496 INFO Out dated connection ,size=0

2025-08-02 00:01:27,496 INFO Connection check task end

2025-08-02 00:01:28,680 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:30,501 INFO Connection check task start

2025-08-02 00:01:30,501 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:30,501 INFO Out dated connection ,size=0

2025-08-02 00:01:30,502 INFO Connection check task end

2025-08-02 00:01:31,685 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:33,504 INFO Connection check task start

2025-08-02 00:01:33,504 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:33,505 INFO Out dated connection ,size=0

2025-08-02 00:01:33,506 INFO Connection check task end

2025-08-02 00:01:34,691 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:36,507 INFO Connection check task start

2025-08-02 00:01:36,507 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:36,508 INFO Out dated connection ,size=0

2025-08-02 00:01:36,508 INFO Connection check task end

2025-08-02 00:01:37,694 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:39,513 INFO Connection check task start

2025-08-02 00:01:39,514 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:39,514 INFO Out dated connection ,size=0

2025-08-02 00:01:39,514 INFO Connection check task end

2025-08-02 00:01:40,697 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:42,518 INFO Connection check task start

2025-08-02 00:01:42,518 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:42,518 INFO Out dated connection ,size=0

2025-08-02 00:01:42,518 INFO Connection check task end

2025-08-02 00:01:43,698 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:45,523 INFO Connection check task start

2025-08-02 00:01:45,523 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:45,523 INFO Out dated connection ,size=0

2025-08-02 00:01:45,523 INFO Connection check task end

2025-08-02 00:01:46,703 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:48,528 INFO Connection check task start

2025-08-02 00:01:48,528 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:48,528 INFO Out dated connection ,size=0

2025-08-02 00:01:48,528 INFO Connection check task end

2025-08-02 00:01:49,706 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:51,532 INFO Connection check task start

2025-08-02 00:01:51,532 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:51,532 INFO Out dated connection ,size=0

2025-08-02 00:01:51,532 INFO Connection check task end

2025-08-02 00:01:52,711 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:54,538 INFO Connection check task start

2025-08-02 00:01:54,538 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:54,538 INFO Out dated connection ,size=0

2025-08-02 00:01:54,538 INFO Connection check task end

2025-08-02 00:01:55,716 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:01:57,541 INFO Connection check task start

2025-08-02 00:01:57,542 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:01:57,542 INFO Out dated connection ,size=0

2025-08-02 00:01:57,542 INFO Connection check task end

2025-08-02 00:01:58,719 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:00,546 INFO Connection check task start

2025-08-02 00:02:00,546 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:00,546 INFO Out dated connection ,size=0

2025-08-02 00:02:00,546 INFO Connection check task end

2025-08-02 00:02:01,725 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:03,551 INFO Connection check task start

2025-08-02 00:02:03,552 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:03,552 INFO Out dated connection ,size=0

2025-08-02 00:02:03,552 INFO Connection check task end

2025-08-02 00:02:04,728 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:06,553 INFO Connection check task start

2025-08-02 00:02:06,554 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:06,554 INFO Out dated connection ,size=0

2025-08-02 00:02:06,554 INFO Connection check task end

2025-08-02 00:02:07,733 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:09,555 INFO Connection check task start

2025-08-02 00:02:09,556 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:09,556 INFO Out dated connection ,size=0

2025-08-02 00:02:09,556 INFO Connection check task end

2025-08-02 00:02:10,739 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:12,558 INFO Connection check task start

2025-08-02 00:02:12,559 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:12,559 INFO Out dated connection ,size=0

2025-08-02 00:02:12,559 INFO Connection check task end

2025-08-02 00:02:13,745 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:15,562 INFO Connection check task start

2025-08-02 00:02:15,563 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:15,563 INFO Out dated connection ,size=0

2025-08-02 00:02:15,563 INFO Connection check task end

2025-08-02 00:02:16,750 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:18,567 INFO Connection check task start

2025-08-02 00:02:18,568 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:18,568 INFO Out dated connection ,size=0

2025-08-02 00:02:18,568 INFO Connection check task end

2025-08-02 00:02:19,754 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:21,573 INFO Connection check task start

2025-08-02 00:02:21,573 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:21,573 INFO Out dated connection ,size=0

2025-08-02 00:02:21,573 INFO Connection check task end

2025-08-02 00:02:22,760 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:24,575 INFO Connection check task start

2025-08-02 00:02:24,575 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:24,576 INFO Out dated connection ,size=0

2025-08-02 00:02:24,576 INFO Connection check task end

2025-08-02 00:02:25,764 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:27,576 INFO Connection check task start

2025-08-02 00:02:27,576 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:27,576 INFO Out dated connection ,size=0

2025-08-02 00:02:27,576 INFO Connection check task end

2025-08-02 00:02:28,770 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:30,580 INFO Connection check task start

2025-08-02 00:02:30,580 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:30,580 INFO Out dated connection ,size=0

2025-08-02 00:02:30,580 INFO Connection check task end

2025-08-02 00:02:31,775 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:33,582 INFO Connection check task start

2025-08-02 00:02:33,582 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:33,582 INFO Out dated connection ,size=0

2025-08-02 00:02:33,583 INFO Connection check task end

2025-08-02 00:02:34,777 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:36,589 INFO Connection check task start

2025-08-02 00:02:36,589 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:36,589 INFO Out dated connection ,size=0

2025-08-02 00:02:36,589 INFO Connection check task end

2025-08-02 00:02:37,778 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:39,593 INFO Connection check task start

2025-08-02 00:02:39,594 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:39,594 INFO Out dated connection ,size=0

2025-08-02 00:02:39,594 INFO Connection check task end

2025-08-02 00:02:40,780 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:42,598 INFO Connection check task start

2025-08-02 00:02:42,598 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:42,599 INFO Out dated connection ,size=0

2025-08-02 00:02:42,599 INFO Connection check task end

2025-08-02 00:02:43,783 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:45,604 INFO Connection check task start

2025-08-02 00:02:45,604 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:45,604 INFO Out dated connection ,size=0

2025-08-02 00:02:45,604 INFO Connection check task end

2025-08-02 00:02:46,789 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:48,606 INFO Connection check task start

2025-08-02 00:02:48,607 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:48,607 INFO Out dated connection ,size=0

2025-08-02 00:02:48,607 INFO Connection check task end

2025-08-02 00:02:49,793 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:51,608 INFO Connection check task start

2025-08-02 00:02:51,608 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:51,608 INFO Out dated connection ,size=0

2025-08-02 00:02:51,608 INFO Connection check task end

2025-08-02 00:02:52,795 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:54,613 INFO Connection check task start

2025-08-02 00:02:54,614 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:54,614 INFO Out dated connection ,size=0

2025-08-02 00:02:54,614 INFO Connection check task end

2025-08-02 00:02:55,800 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:02:57,617 INFO Connection check task start

2025-08-02 00:02:57,618 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:02:57,618 INFO Out dated connection ,size=0

2025-08-02 00:02:57,618 INFO Connection check task end

2025-08-02 00:02:58,803 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:00,623 INFO Connection check task start

2025-08-02 00:03:00,624 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:00,624 INFO Out dated connection ,size=0

2025-08-02 00:03:00,624 INFO Connection check task end

2025-08-02 00:03:01,808 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:03,625 INFO Connection check task start

2025-08-02 00:03:03,626 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:03,626 INFO Out dated connection ,size=0

2025-08-02 00:03:03,626 INFO Connection check task end

2025-08-02 00:03:04,811 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:06,628 INFO Connection check task start

2025-08-02 00:03:06,628 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:06,628 INFO Out dated connection ,size=0

2025-08-02 00:03:06,628 INFO Connection check task end

2025-08-02 00:03:07,812 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:09,630 INFO Connection check task start

2025-08-02 00:03:09,630 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:09,630 INFO Out dated connection ,size=0

2025-08-02 00:03:09,630 INFO Connection check task end

2025-08-02 00:03:10,816 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:12,632 INFO Connection check task start

2025-08-02 00:03:12,632 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:12,633 INFO Out dated connection ,size=0

2025-08-02 00:03:12,633 INFO Connection check task end

2025-08-02 00:03:13,822 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:15,638 INFO Connection check task start

2025-08-02 00:03:15,638 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:15,638 INFO Out dated connection ,size=0

2025-08-02 00:03:15,638 INFO Connection check task end

2025-08-02 00:03:16,826 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:18,643 INFO Connection check task start

2025-08-02 00:03:18,643 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:18,643 INFO Out dated connection ,size=0

2025-08-02 00:03:18,643 INFO Connection check task end

2025-08-02 00:03:19,831 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:21,646 INFO Connection check task start

2025-08-02 00:03:21,646 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:21,646 INFO Out dated connection ,size=0

2025-08-02 00:03:21,646 INFO Connection check task end

2025-08-02 00:03:22,832 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:24,651 INFO Connection check task start

2025-08-02 00:03:24,652 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:24,652 INFO Out dated connection ,size=0

2025-08-02 00:03:24,652 INFO Connection check task end

2025-08-02 00:03:25,833 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:27,654 INFO Connection check task start

2025-08-02 00:03:27,655 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:27,655 INFO Out dated connection ,size=0

2025-08-02 00:03:27,655 INFO Connection check task end

2025-08-02 00:03:28,838 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:30,658 INFO Connection check task start

2025-08-02 00:03:30,659 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:30,659 INFO Out dated connection ,size=0

2025-08-02 00:03:30,659 INFO Connection check task end

2025-08-02 00:03:31,842 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:33,660 INFO Connection check task start

2025-08-02 00:03:33,660 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:33,660 INFO Out dated connection ,size=0

2025-08-02 00:03:33,660 INFO Connection check task end

2025-08-02 00:03:34,846 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:36,664 INFO Connection check task start

2025-08-02 00:03:36,664 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:36,665 INFO Out dated connection ,size=0

2025-08-02 00:03:36,665 INFO Connection check task end

2025-08-02 00:03:37,851 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:39,666 INFO Connection check task start

2025-08-02 00:03:39,668 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:39,668 INFO Out dated connection ,size=0

2025-08-02 00:03:39,668 INFO Connection check task end

2025-08-02 00:03:40,854 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:42,672 INFO Connection check task start

2025-08-02 00:03:42,673 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:42,673 INFO Out dated connection ,size=0

2025-08-02 00:03:42,673 INFO Connection check task end

2025-08-02 00:03:43,859 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:45,678 INFO Connection check task start

2025-08-02 00:03:45,679 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:45,679 INFO Out dated connection ,size=0

2025-08-02 00:03:45,679 INFO Connection check task end

2025-08-02 00:03:46,860 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:48,682 INFO Connection check task start

2025-08-02 00:03:48,683 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:48,683 INFO Out dated connection ,size=0

2025-08-02 00:03:48,683 INFO Connection check task end

2025-08-02 00:03:49,866 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:51,688 INFO Connection check task start

2025-08-02 00:03:51,689 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:51,689 INFO Out dated connection ,size=0

2025-08-02 00:03:51,689 INFO Connection check task end

2025-08-02 00:03:52,869 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:54,691 INFO Connection check task start

2025-08-02 00:03:54,692 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:54,692 INFO Out dated connection ,size=0

2025-08-02 00:03:54,692 INFO Connection check task end

2025-08-02 00:03:55,874 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:03:57,697 INFO Connection check task start

2025-08-02 00:03:57,697 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:03:57,697 INFO Out dated connection ,size=0

2025-08-02 00:03:57,697 INFO Connection check task end

2025-08-02 00:03:58,876 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:00,699 INFO Connection check task start

2025-08-02 00:04:00,699 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:00,699 INFO Out dated connection ,size=0

2025-08-02 00:04:00,699 INFO Connection check task end

2025-08-02 00:04:01,880 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:03,701 INFO Connection check task start

2025-08-02 00:04:03,702 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:03,702 INFO Out dated connection ,size=0

2025-08-02 00:04:03,702 INFO Connection check task end

2025-08-02 00:04:04,885 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:06,707 INFO Connection check task start

2025-08-02 00:04:06,707 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:06,707 INFO Out dated connection ,size=0

2025-08-02 00:04:06,707 INFO Connection check task end

2025-08-02 00:04:07,889 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:09,712 INFO Connection check task start

2025-08-02 00:04:09,712 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:09,712 INFO Out dated connection ,size=0

2025-08-02 00:04:09,712 INFO Connection check task end

2025-08-02 00:04:10,893 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:12,717 INFO Connection check task start

2025-08-02 00:04:12,717 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:12,717 INFO Out dated connection ,size=0

2025-08-02 00:04:12,717 INFO Connection check task end

2025-08-02 00:04:13,899 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:15,721 INFO Connection check task start

2025-08-02 00:04:15,721 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:15,721 INFO Out dated connection ,size=0

2025-08-02 00:04:15,721 INFO Connection check task end

2025-08-02 00:04:16,900 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:18,722 INFO Connection check task start

2025-08-02 00:04:18,722 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:18,722 INFO Out dated connection ,size=0

2025-08-02 00:04:18,722 INFO Connection check task end

2025-08-02 00:04:19,905 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:21,724 INFO Connection check task start

2025-08-02 00:04:21,725 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:21,725 INFO Out dated connection ,size=0

2025-08-02 00:04:21,725 INFO Connection check task end

2025-08-02 00:04:22,909 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:24,729 INFO Connection check task start

2025-08-02 00:04:24,729 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:24,729 INFO Out dated connection ,size=0

2025-08-02 00:04:24,729 INFO Connection check task end

2025-08-02 00:04:25,913 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:27,733 INFO Connection check task start

2025-08-02 00:04:27,733 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:27,733 INFO Out dated connection ,size=0

2025-08-02 00:04:27,734 INFO Connection check task end

2025-08-02 00:04:28,918 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:30,739 INFO Connection check task start

2025-08-02 00:04:30,739 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:30,739 INFO Out dated connection ,size=0

2025-08-02 00:04:30,739 INFO Connection check task end

2025-08-02 00:04:31,921 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:33,742 INFO Connection check task start

2025-08-02 00:04:33,742 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:33,742 INFO Out dated connection ,size=0

2025-08-02 00:04:33,742 INFO Connection check task end

2025-08-02 00:04:34,926 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:36,746 INFO Connection check task start

2025-08-02 00:04:36,746 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:36,746 INFO Out dated connection ,size=0

2025-08-02 00:04:36,746 INFO Connection check task end

2025-08-02 00:04:37,927 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:39,750 INFO Connection check task start

2025-08-02 00:04:39,750 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:39,750 INFO Out dated connection ,size=0

2025-08-02 00:04:39,750 INFO Connection check task end

2025-08-02 00:04:40,932 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:42,755 INFO Connection check task start

2025-08-02 00:04:42,755 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:42,755 INFO Out dated connection ,size=0

2025-08-02 00:04:42,755 INFO Connection check task end

2025-08-02 00:04:43,933 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:45,758 INFO Connection check task start

2025-08-02 00:04:45,758 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:45,759 INFO Out dated connection ,size=0

2025-08-02 00:04:45,760 INFO Connection check task end

2025-08-02 00:04:46,937 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:48,764 INFO Connection check task start

2025-08-02 00:04:48,764 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:48,764 INFO Out dated connection ,size=0

2025-08-02 00:04:48,764 INFO Connection check task end

2025-08-02 00:04:49,938 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:51,766 INFO Connection check task start

2025-08-02 00:04:51,766 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:51,766 INFO Out dated connection ,size=0

2025-08-02 00:04:51,766 INFO Connection check task end

2025-08-02 00:04:52,943 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:54,769 INFO Connection check task start

2025-08-02 00:04:54,769 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:54,769 INFO Out dated connection ,size=0

2025-08-02 00:04:54,769 INFO Connection check task end

2025-08-02 00:04:55,945 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:57,773 INFO Connection check task start

2025-08-02 00:04:57,773 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:04:57,773 INFO Out dated connection ,size=0

2025-08-02 00:04:57,773 INFO Connection check task end

2025-08-02 00:04:58,947 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:04:59,412 INFO [1754058863006_127.0.0.1_63254]Connection unregistered successfully. 

2025-08-02 00:05:00,777 INFO Connection check task start

2025-08-02 00:05:00,777 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:05:00,778 INFO Out dated connection ,size=0

2025-08-02 00:05:00,778 INFO Connection check task end

2025-08-02 00:05:01,964 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:05:03,780 INFO Connection check task start

2025-08-02 00:05:03,780 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:05:03,780 INFO Out dated connection ,size=0

2025-08-02 00:05:03,780 INFO Connection check task end

2025-08-02 00:05:04,966 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:05:06,782 INFO Connection check task start

2025-08-02 00:05:06,782 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:05:06,782 INFO Out dated connection ,size=0

2025-08-02 00:05:06,782 INFO Connection check task end

2025-08-02 00:05:07,972 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:05:09,785 INFO Connection check task start

2025-08-02 00:05:09,785 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:05:09,785 INFO Out dated connection ,size=0

2025-08-02 00:05:09,785 INFO Connection check task end

2025-08-02 00:05:09,832 INFO new connection registered successfully, connectionId = 1754064309804_127.0.0.1_63213,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=63213, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754064309804_127.0.0.1_63213', createTime=Sat Aug 02 00:05:09 CST 2025, lastActiveTime=1754064309831, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:05:10,974 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:12,789 INFO Connection check task start

2025-08-02 00:05:12,789 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:12,789 INFO Out dated connection ,size=0

2025-08-02 00:05:12,789 INFO Connection check task end

2025-08-02 00:05:13,975 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:15,792 INFO Connection check task start

2025-08-02 00:05:15,793 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:15,793 INFO Out dated connection ,size=0

2025-08-02 00:05:15,793 INFO Connection check task end

2025-08-02 00:05:16,980 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:18,796 INFO Connection check task start

2025-08-02 00:05:18,797 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:18,797 INFO Out dated connection ,size=0

2025-08-02 00:05:18,797 INFO Connection check task end

2025-08-02 00:05:19,984 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:21,801 INFO Connection check task start

2025-08-02 00:05:21,801 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:21,801 INFO Out dated connection ,size=0

2025-08-02 00:05:21,801 INFO Connection check task end

2025-08-02 00:05:22,988 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:24,806 INFO Connection check task start

2025-08-02 00:05:24,806 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:24,806 INFO Out dated connection ,size=0

2025-08-02 00:05:24,806 INFO Connection check task end

2025-08-02 00:05:25,993 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:27,809 INFO Connection check task start

2025-08-02 00:05:27,810 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:27,810 INFO Out dated connection ,size=0

2025-08-02 00:05:27,810 INFO Connection check task end

2025-08-02 00:05:28,996 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:30,812 INFO Connection check task start

2025-08-02 00:05:30,812 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:30,812 INFO Out dated connection ,size=0

2025-08-02 00:05:30,812 INFO Connection check task end

2025-08-02 00:05:32,001 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:33,817 INFO Connection check task start

2025-08-02 00:05:33,817 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:33,817 INFO Out dated connection ,size=0

2025-08-02 00:05:33,817 INFO Connection check task end

2025-08-02 00:05:35,003 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:36,819 INFO Connection check task start

2025-08-02 00:05:36,819 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:36,819 INFO Out dated connection ,size=0

2025-08-02 00:05:36,819 INFO Connection check task end

2025-08-02 00:05:38,005 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:39,822 INFO Connection check task start

2025-08-02 00:05:39,822 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:39,822 INFO Out dated connection ,size=0

2025-08-02 00:05:39,822 INFO Connection check task end

2025-08-02 00:05:41,010 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:42,827 INFO Connection check task start

2025-08-02 00:05:42,827 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:42,827 INFO Out dated connection ,size=0

2025-08-02 00:05:42,827 INFO Connection check task end

2025-08-02 00:05:44,015 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:45,828 INFO Connection check task start

2025-08-02 00:05:45,829 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:45,829 INFO Out dated connection ,size=0

2025-08-02 00:05:45,829 INFO Connection check task end

2025-08-02 00:05:47,017 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:48,834 INFO Connection check task start

2025-08-02 00:05:48,834 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:48,834 INFO Out dated connection ,size=0

2025-08-02 00:05:48,834 INFO Connection check task end

2025-08-02 00:05:50,018 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:51,837 INFO Connection check task start

2025-08-02 00:05:51,838 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:51,838 INFO Out dated connection ,size=0

2025-08-02 00:05:51,838 INFO Connection check task end

2025-08-02 00:05:53,022 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:54,840 INFO Connection check task start

2025-08-02 00:05:54,840 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:54,840 INFO Out dated connection ,size=0

2025-08-02 00:05:54,840 INFO Connection check task end

2025-08-02 00:05:56,028 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:05:57,844 INFO Connection check task start

2025-08-02 00:05:57,844 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:05:57,844 INFO Out dated connection ,size=0

2025-08-02 00:05:57,844 INFO Connection check task end

2025-08-02 00:05:59,031 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:00,849 INFO Connection check task start

2025-08-02 00:06:00,850 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:00,850 INFO Out dated connection ,size=0

2025-08-02 00:06:00,850 INFO Connection check task end

2025-08-02 00:06:02,037 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:03,851 INFO Connection check task start

2025-08-02 00:06:03,852 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:03,852 INFO Out dated connection ,size=0

2025-08-02 00:06:03,852 INFO Connection check task end

2025-08-02 00:06:05,042 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:06,856 INFO Connection check task start

2025-08-02 00:06:06,856 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:06,856 INFO Out dated connection ,size=0

2025-08-02 00:06:06,856 INFO Connection check task end

2025-08-02 00:06:08,047 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:09,863 INFO Connection check task start

2025-08-02 00:06:09,863 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:09,863 INFO Out dated connection ,size=0

2025-08-02 00:06:09,865 INFO Connection check task end

2025-08-02 00:06:11,048 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:12,870 INFO Connection check task start

2025-08-02 00:06:12,870 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:12,870 INFO Out dated connection ,size=0

2025-08-02 00:06:12,870 INFO Connection check task end

2025-08-02 00:06:14,052 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:15,872 INFO Connection check task start

2025-08-02 00:06:15,872 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:15,872 INFO Out dated connection ,size=0

2025-08-02 00:06:15,872 INFO Connection check task end

2025-08-02 00:06:17,056 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:18,875 INFO Connection check task start

2025-08-02 00:06:18,875 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:18,875 INFO Out dated connection ,size=0

2025-08-02 00:06:18,875 INFO Connection check task end

2025-08-02 00:06:20,060 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:20,919 INFO [capacityManagement] start correct usage

2025-08-02 00:06:20,919 INFO [MapperManager] findMapper dataSource: mysql, tableName: group_capacity

2025-08-02 00:06:20,921 INFO [MapperManager] findMapper dataSource: mysql, tableName: tenant_capacity

2025-08-02 00:06:20,921 INFO [capacityManagement] end correct usage, cost: 0.001507s

2025-08-02 00:06:20,922 WARN clearConfigHistory start

2025-08-02 00:06:20,922 WARN clearConfigHistory, getBeforeStamp:2025-07-03 00:06:20.0, pageSize:1000

2025-08-02 00:06:20,922 INFO [MapperManager] findMapper dataSource: mysql, tableName: his_config_info

2025-08-02 00:06:21,880 INFO Connection check task start

2025-08-02 00:06:21,880 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:21,880 INFO Out dated connection ,size=0

2025-08-02 00:06:21,881 INFO Connection check task end

2025-08-02 00:06:23,065 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:24,884 INFO Connection check task start

2025-08-02 00:06:24,884 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:24,884 INFO Out dated connection ,size=0

2025-08-02 00:06:24,884 INFO Connection check task end

2025-08-02 00:06:25,107 INFO [1754064309804_127.0.0.1_63213]Connection unregistered successfully. 

2025-08-02 00:06:26,071 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:06:27,892 INFO Connection check task start

2025-08-02 00:06:27,893 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:06:27,894 INFO Out dated connection ,size=0

2025-08-02 00:06:27,894 INFO Connection check task end

2025-08-02 00:06:29,076 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:06:30,899 INFO Connection check task start

2025-08-02 00:06:30,899 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:06:30,899 INFO Out dated connection ,size=0

2025-08-02 00:06:30,899 INFO Connection check task end

2025-08-02 00:06:32,080 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:06:33,900 INFO Connection check task start

2025-08-02 00:06:33,901 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:06:33,901 INFO Out dated connection ,size=0

2025-08-02 00:06:33,901 INFO Connection check task end

2025-08-02 00:06:35,084 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:06:36,816 INFO new connection registered successfully, connectionId = 1754064396786_127.0.0.1_63717,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=63717, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754064396786_127.0.0.1_63717', createTime=Sat Aug 02 00:06:36 CST 2025, lastActiveTime=1754064396816, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:06:36,906 INFO Connection check task start

2025-08-02 00:06:36,906 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:36,906 INFO Out dated connection ,size=0

2025-08-02 00:06:36,906 INFO Connection check task end

2025-08-02 00:06:38,084 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:39,909 INFO Connection check task start

2025-08-02 00:06:39,909 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:39,909 INFO Out dated connection ,size=0

2025-08-02 00:06:39,909 INFO Connection check task end

2025-08-02 00:06:41,089 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:42,913 INFO Connection check task start

2025-08-02 00:06:42,913 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:42,913 INFO Out dated connection ,size=0

2025-08-02 00:06:42,913 INFO Connection check task end

2025-08-02 00:06:44,090 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:45,915 INFO Connection check task start

2025-08-02 00:06:45,915 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:45,915 INFO Out dated connection ,size=0

2025-08-02 00:06:45,915 INFO Connection check task end

2025-08-02 00:06:47,092 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:48,916 INFO Connection check task start

2025-08-02 00:06:48,917 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:48,917 INFO Out dated connection ,size=0

2025-08-02 00:06:48,917 INFO Connection check task end

2025-08-02 00:06:50,095 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:51,922 INFO Connection check task start

2025-08-02 00:06:51,922 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:51,922 INFO Out dated connection ,size=0

2025-08-02 00:06:51,922 INFO Connection check task end

2025-08-02 00:06:53,098 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:54,927 INFO Connection check task start

2025-08-02 00:06:54,928 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:54,928 INFO Out dated connection ,size=0

2025-08-02 00:06:54,928 INFO Connection check task end

2025-08-02 00:06:56,098 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:06:57,932 INFO Connection check task start

2025-08-02 00:06:57,932 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:06:57,932 INFO Out dated connection ,size=0

2025-08-02 00:06:57,932 INFO Connection check task end

2025-08-02 00:06:59,104 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:00,936 INFO Connection check task start

2025-08-02 00:07:00,936 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:00,936 INFO Out dated connection ,size=0

2025-08-02 00:07:00,936 INFO Connection check task end

2025-08-02 00:07:02,109 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:03,939 INFO Connection check task start

2025-08-02 00:07:03,939 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:03,939 INFO Out dated connection ,size=0

2025-08-02 00:07:03,939 INFO Connection check task end

2025-08-02 00:07:05,114 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:06,940 INFO Connection check task start

2025-08-02 00:07:06,940 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:06,940 INFO Out dated connection ,size=0

2025-08-02 00:07:06,940 INFO Connection check task end

2025-08-02 00:07:08,118 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:09,941 INFO Connection check task start

2025-08-02 00:07:09,941 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:09,941 INFO Out dated connection ,size=0

2025-08-02 00:07:09,941 INFO Connection check task end

2025-08-02 00:07:11,123 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:12,945 INFO Connection check task start

2025-08-02 00:07:12,945 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:12,945 INFO Out dated connection ,size=0

2025-08-02 00:07:12,946 INFO Connection check task end

2025-08-02 00:07:14,127 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:15,947 INFO Connection check task start

2025-08-02 00:07:15,947 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:15,948 INFO Out dated connection ,size=0

2025-08-02 00:07:15,948 INFO Connection check task end

2025-08-02 00:07:17,130 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:18,949 INFO Connection check task start

2025-08-02 00:07:18,949 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:18,949 INFO Out dated connection ,size=0

2025-08-02 00:07:18,949 INFO Connection check task end

2025-08-02 00:07:20,134 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:21,951 INFO Connection check task start

2025-08-02 00:07:21,951 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:21,951 INFO Out dated connection ,size=0

2025-08-02 00:07:21,951 INFO Connection check task end

2025-08-02 00:07:23,138 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:24,955 INFO Connection check task start

2025-08-02 00:07:24,956 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:24,956 INFO Out dated connection ,size=0

2025-08-02 00:07:24,956 INFO Connection check task end

2025-08-02 00:07:26,139 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:27,961 INFO Connection check task start

2025-08-02 00:07:27,961 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:27,961 INFO Out dated connection ,size=0

2025-08-02 00:07:27,961 INFO Connection check task end

2025-08-02 00:07:29,143 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:30,964 INFO Connection check task start

2025-08-02 00:07:30,965 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:30,965 INFO Out dated connection ,size=0

2025-08-02 00:07:30,965 INFO Connection check task end

2025-08-02 00:07:32,147 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:33,968 INFO Connection check task start

2025-08-02 00:07:33,968 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:33,968 INFO Out dated connection ,size=0

2025-08-02 00:07:33,968 INFO Connection check task end

2025-08-02 00:07:35,151 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:36,972 INFO Connection check task start

2025-08-02 00:07:36,973 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:36,973 INFO Out dated connection ,size=0

2025-08-02 00:07:36,973 INFO Connection check task end

2025-08-02 00:07:38,157 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:39,977 INFO Connection check task start

2025-08-02 00:07:39,978 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:39,978 INFO Out dated connection ,size=0

2025-08-02 00:07:39,978 INFO Connection check task end

2025-08-02 00:07:41,159 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:42,982 INFO Connection check task start

2025-08-02 00:07:42,982 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:42,982 INFO Out dated connection ,size=0

2025-08-02 00:07:42,982 INFO Connection check task end

2025-08-02 00:07:44,163 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:45,988 INFO Connection check task start

2025-08-02 00:07:45,988 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:45,988 INFO Out dated connection ,size=0

2025-08-02 00:07:45,988 INFO Connection check task end

2025-08-02 00:07:47,165 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:48,990 INFO Connection check task start

2025-08-02 00:07:48,991 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:48,991 INFO Out dated connection ,size=0

2025-08-02 00:07:48,991 INFO Connection check task end

2025-08-02 00:07:50,168 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:51,995 INFO Connection check task start

2025-08-02 00:07:51,996 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:51,996 INFO Out dated connection ,size=0

2025-08-02 00:07:51,996 INFO Connection check task end

2025-08-02 00:07:53,172 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:54,998 INFO Connection check task start

2025-08-02 00:07:54,999 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:54,999 INFO Out dated connection ,size=0

2025-08-02 00:07:54,999 INFO Connection check task end

2025-08-02 00:07:56,175 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:07:58,001 INFO Connection check task start

2025-08-02 00:07:58,001 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:07:58,001 INFO Out dated connection ,size=0

2025-08-02 00:07:58,002 INFO Connection check task end

2025-08-02 00:07:59,181 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:01,006 INFO Connection check task start

2025-08-02 00:08:01,007 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:01,007 INFO Out dated connection ,size=0

2025-08-02 00:08:01,007 INFO Connection check task end

2025-08-02 00:08:02,186 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:04,012 INFO Connection check task start

2025-08-02 00:08:04,013 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:04,013 INFO Out dated connection ,size=0

2025-08-02 00:08:04,013 INFO Connection check task end

2025-08-02 00:08:05,190 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:07,013 INFO Connection check task start

2025-08-02 00:08:07,013 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:07,013 INFO Out dated connection ,size=0

2025-08-02 00:08:07,013 INFO Connection check task end

2025-08-02 00:08:08,191 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:10,018 INFO Connection check task start

2025-08-02 00:08:10,018 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:10,018 INFO Out dated connection ,size=0

2025-08-02 00:08:10,018 INFO Connection check task end

2025-08-02 00:08:11,197 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:13,024 INFO Connection check task start

2025-08-02 00:08:13,024 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:13,024 INFO Out dated connection ,size=0

2025-08-02 00:08:13,024 INFO Connection check task end

2025-08-02 00:08:14,200 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:16,027 INFO Connection check task start

2025-08-02 00:08:16,027 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:16,027 INFO Out dated connection ,size=0

2025-08-02 00:08:16,027 INFO Connection check task end

2025-08-02 00:08:17,201 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:19,030 INFO Connection check task start

2025-08-02 00:08:19,031 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:19,031 INFO Out dated connection ,size=0

2025-08-02 00:08:19,031 INFO Connection check task end

2025-08-02 00:08:20,206 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:22,036 INFO Connection check task start

2025-08-02 00:08:22,036 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:22,036 INFO Out dated connection ,size=0

2025-08-02 00:08:22,036 INFO Connection check task end

2025-08-02 00:08:23,209 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:25,039 INFO Connection check task start

2025-08-02 00:08:25,039 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:25,039 INFO Out dated connection ,size=0

2025-08-02 00:08:25,039 INFO Connection check task end

2025-08-02 00:08:26,213 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:28,040 INFO Connection check task start

2025-08-02 00:08:28,041 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:28,041 INFO Out dated connection ,size=0

2025-08-02 00:08:28,041 INFO Connection check task end

2025-08-02 00:08:29,219 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:31,046 INFO Connection check task start

2025-08-02 00:08:31,047 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:31,047 INFO Out dated connection ,size=0

2025-08-02 00:08:31,047 INFO Connection check task end

2025-08-02 00:08:32,222 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:34,050 INFO Connection check task start

2025-08-02 00:08:34,051 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:34,051 INFO Out dated connection ,size=0

2025-08-02 00:08:34,051 INFO Connection check task end

2025-08-02 00:08:35,226 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:37,055 INFO Connection check task start

2025-08-02 00:08:37,056 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:37,056 INFO Out dated connection ,size=0

2025-08-02 00:08:37,056 INFO Connection check task end

2025-08-02 00:08:38,227 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:40,061 INFO Connection check task start

2025-08-02 00:08:40,061 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:40,061 INFO Out dated connection ,size=0

2025-08-02 00:08:40,061 INFO Connection check task end

2025-08-02 00:08:41,229 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:43,067 INFO Connection check task start

2025-08-02 00:08:43,067 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:43,067 INFO Out dated connection ,size=0

2025-08-02 00:08:43,067 INFO Connection check task end

2025-08-02 00:08:44,234 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:46,069 INFO Connection check task start

2025-08-02 00:08:46,070 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:46,070 INFO Out dated connection ,size=0

2025-08-02 00:08:46,070 INFO Connection check task end

2025-08-02 00:08:47,240 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:49,075 INFO Connection check task start

2025-08-02 00:08:49,075 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:49,075 INFO Out dated connection ,size=0

2025-08-02 00:08:49,075 INFO Connection check task end

2025-08-02 00:08:50,245 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:52,080 INFO Connection check task start

2025-08-02 00:08:52,081 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:52,081 INFO Out dated connection ,size=0

2025-08-02 00:08:52,081 INFO Connection check task end

2025-08-02 00:08:53,247 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:55,083 INFO Connection check task start

2025-08-02 00:08:55,084 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:55,084 INFO Out dated connection ,size=0

2025-08-02 00:08:55,084 INFO Connection check task end

2025-08-02 00:08:56,251 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:08:58,088 INFO Connection check task start

2025-08-02 00:08:58,089 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:08:58,089 INFO Out dated connection ,size=0

2025-08-02 00:08:58,089 INFO Connection check task end

2025-08-02 00:08:59,256 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:01,091 INFO Connection check task start

2025-08-02 00:09:01,092 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:01,092 INFO Out dated connection ,size=0

2025-08-02 00:09:01,092 INFO Connection check task end

2025-08-02 00:09:02,262 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:04,093 INFO Connection check task start

2025-08-02 00:09:04,093 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:04,094 INFO Out dated connection ,size=0

2025-08-02 00:09:04,094 INFO Connection check task end

2025-08-02 00:09:05,266 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:07,098 INFO Connection check task start

2025-08-02 00:09:07,098 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:07,098 INFO Out dated connection ,size=0

2025-08-02 00:09:07,099 INFO Connection check task end

2025-08-02 00:09:08,270 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:10,103 INFO Connection check task start

2025-08-02 00:09:10,103 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:10,104 INFO Out dated connection ,size=0

2025-08-02 00:09:10,104 INFO Connection check task end

2025-08-02 00:09:11,275 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:13,106 INFO Connection check task start

2025-08-02 00:09:13,106 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:13,106 INFO Out dated connection ,size=0

2025-08-02 00:09:13,106 INFO Connection check task end

2025-08-02 00:09:14,277 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:16,111 INFO Connection check task start

2025-08-02 00:09:16,112 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:16,112 INFO Out dated connection ,size=0

2025-08-02 00:09:16,112 INFO Connection check task end

2025-08-02 00:09:17,282 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:19,116 INFO Connection check task start

2025-08-02 00:09:19,116 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:19,116 INFO Out dated connection ,size=0

2025-08-02 00:09:19,116 INFO Connection check task end

2025-08-02 00:09:20,288 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:22,121 INFO Connection check task start

2025-08-02 00:09:22,121 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:22,121 INFO Out dated connection ,size=0

2025-08-02 00:09:22,121 INFO Connection check task end

2025-08-02 00:09:23,291 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:25,127 INFO Connection check task start

2025-08-02 00:09:25,127 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:25,127 INFO Out dated connection ,size=0

2025-08-02 00:09:25,127 INFO Connection check task end

2025-08-02 00:09:26,297 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:28,131 INFO Connection check task start

2025-08-02 00:09:28,132 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:28,132 INFO Out dated connection ,size=0

2025-08-02 00:09:28,132 INFO Connection check task end

2025-08-02 00:09:29,303 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:31,133 INFO Connection check task start

2025-08-02 00:09:31,134 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:31,134 INFO Out dated connection ,size=0

2025-08-02 00:09:31,134 INFO Connection check task end

2025-08-02 00:09:32,306 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:34,139 INFO Connection check task start

2025-08-02 00:09:34,139 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:34,140 INFO Out dated connection ,size=0

2025-08-02 00:09:34,140 INFO Connection check task end

2025-08-02 00:09:35,308 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:37,144 INFO Connection check task start

2025-08-02 00:09:37,145 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:37,145 INFO Out dated connection ,size=0

2025-08-02 00:09:37,145 INFO Connection check task end

2025-08-02 00:09:38,310 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:40,149 INFO Connection check task start

2025-08-02 00:09:40,149 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:40,149 INFO Out dated connection ,size=0

2025-08-02 00:09:40,149 INFO Connection check task end

2025-08-02 00:09:41,316 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:43,152 INFO Connection check task start

2025-08-02 00:09:43,152 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:43,152 INFO Out dated connection ,size=0

2025-08-02 00:09:43,152 INFO Connection check task end

2025-08-02 00:09:44,318 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:46,157 INFO Connection check task start

2025-08-02 00:09:46,158 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:46,158 INFO Out dated connection ,size=0

2025-08-02 00:09:46,158 INFO Connection check task end

2025-08-02 00:09:47,323 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:49,160 INFO Connection check task start

2025-08-02 00:09:49,160 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:49,160 INFO Out dated connection ,size=0

2025-08-02 00:09:49,160 INFO Connection check task end

2025-08-02 00:09:50,329 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:52,161 INFO Connection check task start

2025-08-02 00:09:52,161 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:52,161 INFO Out dated connection ,size=0

2025-08-02 00:09:52,161 INFO Connection check task end

2025-08-02 00:09:53,333 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:55,164 INFO Connection check task start

2025-08-02 00:09:55,165 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:55,166 INFO Out dated connection ,size=0

2025-08-02 00:09:55,166 INFO Connection check task end

2025-08-02 00:09:56,335 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:09:58,168 INFO Connection check task start

2025-08-02 00:09:58,169 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:09:58,169 INFO Out dated connection ,size=0

2025-08-02 00:09:58,169 INFO Connection check task end

2025-08-02 00:09:59,337 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:01,173 INFO Connection check task start

2025-08-02 00:10:01,173 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:01,173 INFO Out dated connection ,size=0

2025-08-02 00:10:01,173 INFO Connection check task end

2025-08-02 00:10:02,343 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:04,178 INFO Connection check task start

2025-08-02 00:10:04,178 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:04,178 INFO Out dated connection ,size=0

2025-08-02 00:10:04,178 INFO Connection check task end

2025-08-02 00:10:05,347 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:07,181 INFO Connection check task start

2025-08-02 00:10:07,181 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:07,181 INFO Out dated connection ,size=0

2025-08-02 00:10:07,181 INFO Connection check task end

2025-08-02 00:10:08,350 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:10,183 INFO Connection check task start

2025-08-02 00:10:10,183 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:10,184 INFO Out dated connection ,size=0

2025-08-02 00:10:10,184 INFO Connection check task end

2025-08-02 00:10:11,361 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:13,194 INFO Connection check task start

2025-08-02 00:10:13,194 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:13,194 INFO Out dated connection ,size=0

2025-08-02 00:10:13,194 INFO Connection check task end

2025-08-02 00:10:14,364 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:16,199 INFO Connection check task start

2025-08-02 00:10:16,200 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:16,200 INFO Out dated connection ,size=0

2025-08-02 00:10:16,200 INFO Connection check task end

2025-08-02 00:10:17,370 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:19,210 INFO Connection check task start

2025-08-02 00:10:19,211 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:19,211 INFO Out dated connection ,size=0

2025-08-02 00:10:19,211 INFO Connection check task end

2025-08-02 00:10:20,375 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:22,219 INFO Connection check task start

2025-08-02 00:10:22,219 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:22,219 INFO Out dated connection ,size=0

2025-08-02 00:10:22,219 INFO Connection check task end

2025-08-02 00:10:23,385 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:25,227 INFO Connection check task start

2025-08-02 00:10:25,227 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:25,228 INFO Out dated connection ,size=0

2025-08-02 00:10:25,228 INFO Connection check task end

2025-08-02 00:10:26,386 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:28,229 INFO Connection check task start

2025-08-02 00:10:28,229 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:28,229 INFO Out dated connection ,size=0

2025-08-02 00:10:28,229 INFO Connection check task end

2025-08-02 00:10:29,389 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:31,239 INFO Connection check task start

2025-08-02 00:10:31,239 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:31,239 INFO Out dated connection ,size=0

2025-08-02 00:10:31,239 INFO Connection check task end

2025-08-02 00:10:32,397 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:34,245 INFO Connection check task start

2025-08-02 00:10:34,246 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:34,246 INFO Out dated connection ,size=0

2025-08-02 00:10:34,246 INFO Connection check task end

2025-08-02 00:10:35,400 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:37,256 INFO Connection check task start

2025-08-02 00:10:37,257 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:37,257 INFO Out dated connection ,size=0

2025-08-02 00:10:37,257 INFO Connection check task end

2025-08-02 00:10:38,405 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:40,266 INFO Connection check task start

2025-08-02 00:10:40,267 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:40,267 INFO Out dated connection ,size=0

2025-08-02 00:10:40,267 INFO Connection check task end

2025-08-02 00:10:41,410 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:43,274 INFO Connection check task start

2025-08-02 00:10:43,275 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:43,275 INFO Out dated connection ,size=0

2025-08-02 00:10:43,275 INFO Connection check task end

2025-08-02 00:10:44,417 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:46,285 INFO Connection check task start

2025-08-02 00:10:46,285 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:46,285 INFO Out dated connection ,size=0

2025-08-02 00:10:46,285 INFO Connection check task end

2025-08-02 00:10:47,427 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:49,292 INFO Connection check task start

2025-08-02 00:10:49,292 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:49,292 INFO Out dated connection ,size=0

2025-08-02 00:10:49,292 INFO Connection check task end

2025-08-02 00:10:50,433 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:52,302 INFO Connection check task start

2025-08-02 00:10:52,302 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:52,302 INFO Out dated connection ,size=0

2025-08-02 00:10:52,303 INFO Connection check task end

2025-08-02 00:10:53,444 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:55,310 INFO Connection check task start

2025-08-02 00:10:55,311 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:10:55,311 INFO Out dated connection ,size=0

2025-08-02 00:10:55,311 INFO Connection check task end

2025-08-02 00:10:56,453 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:10:57,003 INFO [1754057710652_127.0.0.1_58891]Connection unregistered successfully. 

2025-08-02 00:10:58,317 INFO Connection check task start

2025-08-02 00:10:58,317 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:10:58,317 INFO Out dated connection ,size=0

2025-08-02 00:10:58,318 INFO Connection check task end

2025-08-02 00:10:59,381 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:11:01,248 INFO Connection check task start

2025-08-02 00:11:01,249 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:11:01,249 INFO Out dated connection ,size=0

2025-08-02 00:11:01,249 INFO Connection check task end

2025-08-02 00:11:02,385 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:11:04,257 INFO Connection check task start

2025-08-02 00:11:04,257 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:11:04,257 INFO Out dated connection ,size=0

2025-08-02 00:11:04,257 INFO Connection check task end

2025-08-02 00:11:04,897 INFO new connection registered successfully, connectionId = 1754064664870_127.0.0.1_65169,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=65169, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754064664870_127.0.0.1_65169', createTime=Sat Aug 02 00:11:04 CST 2025, lastActiveTime=1754064664897, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:11:05,388 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:07,263 INFO Connection check task start

2025-08-02 00:11:07,263 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:07,263 INFO Out dated connection ,size=0

2025-08-02 00:11:07,263 INFO Connection check task end

2025-08-02 00:11:08,390 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:10,269 INFO Connection check task start

2025-08-02 00:11:10,269 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:10,269 INFO Out dated connection ,size=0

2025-08-02 00:11:10,269 INFO Connection check task end

2025-08-02 00:11:11,396 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:13,279 INFO Connection check task start

2025-08-02 00:11:13,280 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:13,280 INFO Out dated connection ,size=0

2025-08-02 00:11:13,280 INFO Connection check task end

2025-08-02 00:11:14,406 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:16,285 INFO Connection check task start

2025-08-02 00:11:16,286 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:16,286 INFO Out dated connection ,size=0

2025-08-02 00:11:16,286 INFO Connection check task end

2025-08-02 00:11:17,416 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:19,290 INFO Connection check task start

2025-08-02 00:11:19,291 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:19,291 INFO Out dated connection ,size=0

2025-08-02 00:11:19,291 INFO Connection check task end

2025-08-02 00:11:20,423 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:22,292 INFO Connection check task start

2025-08-02 00:11:22,292 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:22,292 INFO Out dated connection ,size=0

2025-08-02 00:11:22,292 INFO Connection check task end

2025-08-02 00:11:23,432 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:25,293 INFO Connection check task start

2025-08-02 00:11:25,293 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:25,293 INFO Out dated connection ,size=0

2025-08-02 00:11:25,293 INFO Connection check task end

2025-08-02 00:11:26,438 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:28,295 INFO Connection check task start

2025-08-02 00:11:28,296 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:28,296 INFO Out dated connection ,size=0

2025-08-02 00:11:28,296 INFO Connection check task end

2025-08-02 00:11:29,448 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:31,303 INFO Connection check task start

2025-08-02 00:11:31,303 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:31,303 INFO Out dated connection ,size=0

2025-08-02 00:11:31,303 INFO Connection check task end

2025-08-02 00:11:32,457 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:34,303 INFO Connection check task start

2025-08-02 00:11:34,304 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:34,304 INFO Out dated connection ,size=0

2025-08-02 00:11:34,304 INFO Connection check task end

2025-08-02 00:11:35,462 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:37,305 INFO Connection check task start

2025-08-02 00:11:37,305 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:37,305 INFO Out dated connection ,size=0

2025-08-02 00:11:37,305 INFO Connection check task end

2025-08-02 00:11:38,472 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:40,309 INFO Connection check task start

2025-08-02 00:11:40,309 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:40,309 INFO Out dated connection ,size=0

2025-08-02 00:11:40,309 INFO Connection check task end

2025-08-02 00:11:41,478 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:43,315 INFO Connection check task start

2025-08-02 00:11:43,315 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:43,315 INFO Out dated connection ,size=0

2025-08-02 00:11:43,315 INFO Connection check task end

2025-08-02 00:11:44,487 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:46,325 INFO Connection check task start

2025-08-02 00:11:46,325 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:46,325 INFO Out dated connection ,size=0

2025-08-02 00:11:46,325 INFO Connection check task end

2025-08-02 00:11:47,490 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:49,331 INFO Connection check task start

2025-08-02 00:11:49,331 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:49,331 INFO Out dated connection ,size=0

2025-08-02 00:11:49,331 INFO Connection check task end

2025-08-02 00:11:50,499 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:52,334 INFO Connection check task start

2025-08-02 00:11:52,334 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:52,334 INFO Out dated connection ,size=0

2025-08-02 00:11:52,334 INFO Connection check task end

2025-08-02 00:11:53,502 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:55,344 INFO Connection check task start

2025-08-02 00:11:55,345 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:55,345 INFO Out dated connection ,size=0

2025-08-02 00:11:55,345 INFO Connection check task end

2025-08-02 00:11:56,512 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:11:58,350 INFO Connection check task start

2025-08-02 00:11:58,350 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:11:58,350 INFO Out dated connection ,size=0

2025-08-02 00:11:58,350 INFO Connection check task end

2025-08-02 00:11:59,515 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:01,359 INFO Connection check task start

2025-08-02 00:12:01,359 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:01,360 INFO Out dated connection ,size=0

2025-08-02 00:12:01,360 INFO Connection check task end

2025-08-02 00:12:02,524 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:04,364 INFO Connection check task start

2025-08-02 00:12:04,364 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:04,364 INFO Out dated connection ,size=0

2025-08-02 00:12:04,364 INFO Connection check task end

2025-08-02 00:12:05,528 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:07,368 INFO Connection check task start

2025-08-02 00:12:07,368 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:07,368 INFO Out dated connection ,size=0

2025-08-02 00:12:07,368 INFO Connection check task end

2025-08-02 00:12:08,538 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:10,371 INFO Connection check task start

2025-08-02 00:12:10,371 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:10,371 INFO Out dated connection ,size=0

2025-08-02 00:12:10,371 INFO Connection check task end

2025-08-02 00:12:11,538 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:13,375 INFO Connection check task start

2025-08-02 00:12:13,376 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:13,376 INFO Out dated connection ,size=0

2025-08-02 00:12:13,376 INFO Connection check task end

2025-08-02 00:12:14,539 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:16,383 INFO Connection check task start

2025-08-02 00:12:16,383 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:16,383 INFO Out dated connection ,size=0

2025-08-02 00:12:16,383 INFO Connection check task end

2025-08-02 00:12:17,545 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:19,383 INFO Connection check task start

2025-08-02 00:12:19,383 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:19,383 INFO Out dated connection ,size=0

2025-08-02 00:12:19,383 INFO Connection check task end

2025-08-02 00:12:20,555 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:22,386 INFO Connection check task start

2025-08-02 00:12:22,387 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:22,387 INFO Out dated connection ,size=0

2025-08-02 00:12:22,387 INFO Connection check task end

2025-08-02 00:12:23,565 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:25,393 INFO Connection check task start

2025-08-02 00:12:25,393 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:25,393 INFO Out dated connection ,size=0

2025-08-02 00:12:25,393 INFO Connection check task end

2025-08-02 00:12:26,572 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:28,403 INFO Connection check task start

2025-08-02 00:12:28,403 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:28,403 INFO Out dated connection ,size=0

2025-08-02 00:12:28,403 INFO Connection check task end

2025-08-02 00:12:29,582 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:31,403 INFO Connection check task start

2025-08-02 00:12:31,404 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:31,404 INFO Out dated connection ,size=0

2025-08-02 00:12:31,404 INFO Connection check task end

2025-08-02 00:12:32,587 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:34,407 INFO Connection check task start

2025-08-02 00:12:34,408 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:34,408 INFO Out dated connection ,size=0

2025-08-02 00:12:34,408 INFO Connection check task end

2025-08-02 00:12:35,596 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:37,416 INFO Connection check task start

2025-08-02 00:12:37,416 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:37,416 INFO Out dated connection ,size=0

2025-08-02 00:12:37,416 INFO Connection check task end

2025-08-02 00:12:38,604 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:40,422 INFO Connection check task start

2025-08-02 00:12:40,422 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:40,422 INFO Out dated connection ,size=0

2025-08-02 00:12:40,422 INFO Connection check task end

2025-08-02 00:12:41,611 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:43,433 INFO Connection check task start

2025-08-02 00:12:43,433 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:43,433 INFO Out dated connection ,size=0

2025-08-02 00:12:43,433 INFO Connection check task end

2025-08-02 00:12:44,621 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:46,439 INFO Connection check task start

2025-08-02 00:12:46,439 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:46,439 INFO Out dated connection ,size=0

2025-08-02 00:12:46,439 INFO Connection check task end

2025-08-02 00:12:47,625 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:49,444 INFO Connection check task start

2025-08-02 00:12:49,444 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:49,444 INFO Out dated connection ,size=0

2025-08-02 00:12:49,444 INFO Connection check task end

2025-08-02 00:12:50,629 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:52,449 INFO Connection check task start

2025-08-02 00:12:52,449 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:52,449 INFO Out dated connection ,size=0

2025-08-02 00:12:52,449 INFO Connection check task end

2025-08-02 00:12:53,633 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:55,453 INFO Connection check task start

2025-08-02 00:12:55,453 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:55,453 INFO Out dated connection ,size=0

2025-08-02 00:12:55,453 INFO Connection check task end

2025-08-02 00:12:56,637 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:12:58,455 INFO Connection check task start

2025-08-02 00:12:58,455 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:12:58,455 INFO Out dated connection ,size=0

2025-08-02 00:12:58,455 INFO Connection check task end

2025-08-02 00:12:59,642 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:01,464 INFO Connection check task start

2025-08-02 00:13:01,464 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:01,464 INFO Out dated connection ,size=0

2025-08-02 00:13:01,464 INFO Connection check task end

2025-08-02 00:13:02,651 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:04,474 INFO Connection check task start

2025-08-02 00:13:04,475 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:04,475 INFO Out dated connection ,size=0

2025-08-02 00:13:04,475 INFO Connection check task end

2025-08-02 00:13:05,656 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:07,481 INFO Connection check task start

2025-08-02 00:13:07,481 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:07,482 INFO Out dated connection ,size=0

2025-08-02 00:13:07,482 INFO Connection check task end

2025-08-02 00:13:08,662 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:10,488 INFO Connection check task start

2025-08-02 00:13:10,488 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:10,488 INFO Out dated connection ,size=0

2025-08-02 00:13:10,488 INFO Connection check task end

2025-08-02 00:13:11,663 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:13,497 INFO Connection check task start

2025-08-02 00:13:13,497 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:13,497 INFO Out dated connection ,size=0

2025-08-02 00:13:13,497 INFO Connection check task end

2025-08-02 00:13:14,672 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:16,506 INFO Connection check task start

2025-08-02 00:13:16,506 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:16,506 INFO Out dated connection ,size=0

2025-08-02 00:13:16,507 INFO Connection check task end

2025-08-02 00:13:17,676 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:19,508 INFO Connection check task start

2025-08-02 00:13:19,509 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:19,509 INFO Out dated connection ,size=0

2025-08-02 00:13:19,509 INFO Connection check task end

2025-08-02 00:13:20,685 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:22,519 INFO Connection check task start

2025-08-02 00:13:22,519 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:22,520 INFO Out dated connection ,size=0

2025-08-02 00:13:22,520 INFO Connection check task end

2025-08-02 00:13:23,693 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:25,530 INFO Connection check task start

2025-08-02 00:13:25,530 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:25,530 INFO Out dated connection ,size=0

2025-08-02 00:13:25,530 INFO Connection check task end

2025-08-02 00:13:26,700 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:28,539 INFO Connection check task start

2025-08-02 00:13:28,539 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:28,539 INFO Out dated connection ,size=0

2025-08-02 00:13:28,539 INFO Connection check task end

2025-08-02 00:13:29,700 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:31,543 INFO Connection check task start

2025-08-02 00:13:31,543 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:31,543 INFO Out dated connection ,size=0

2025-08-02 00:13:31,543 INFO Connection check task end

2025-08-02 00:13:32,711 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:34,553 INFO Connection check task start

2025-08-02 00:13:34,553 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:34,553 INFO Out dated connection ,size=0

2025-08-02 00:13:34,554 INFO Connection check task end

2025-08-02 00:13:35,718 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:37,556 INFO Connection check task start

2025-08-02 00:13:37,557 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:37,557 INFO Out dated connection ,size=0

2025-08-02 00:13:37,557 INFO Connection check task end

2025-08-02 00:13:38,718 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:40,559 INFO Connection check task start

2025-08-02 00:13:40,560 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:40,560 INFO Out dated connection ,size=0

2025-08-02 00:13:40,560 INFO Connection check task end

2025-08-02 00:13:41,728 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:43,569 INFO Connection check task start

2025-08-02 00:13:43,569 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:43,569 INFO Out dated connection ,size=0

2025-08-02 00:13:43,569 INFO Connection check task end

2025-08-02 00:13:44,737 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:46,573 INFO Connection check task start

2025-08-02 00:13:46,573 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:46,573 INFO Out dated connection ,size=0

2025-08-02 00:13:46,573 INFO Connection check task end

2025-08-02 00:13:47,739 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:49,581 INFO Connection check task start

2025-08-02 00:13:49,581 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:49,582 INFO Out dated connection ,size=0

2025-08-02 00:13:49,582 INFO Connection check task end

2025-08-02 00:13:50,749 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:52,588 INFO Connection check task start

2025-08-02 00:13:52,588 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:52,588 INFO Out dated connection ,size=0

2025-08-02 00:13:52,588 INFO Connection check task end

2025-08-02 00:13:53,758 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:55,597 INFO Connection check task start

2025-08-02 00:13:55,597 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:55,598 INFO Out dated connection ,size=0

2025-08-02 00:13:55,598 INFO Connection check task end

2025-08-02 00:13:56,761 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:13:58,601 INFO Connection check task start

2025-08-02 00:13:58,602 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:13:58,602 INFO Out dated connection ,size=0

2025-08-02 00:13:58,602 INFO Connection check task end

2025-08-02 00:13:59,768 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:01,606 INFO Connection check task start

2025-08-02 00:14:01,606 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:01,606 INFO Out dated connection ,size=0

2025-08-02 00:14:01,606 INFO Connection check task end

2025-08-02 00:14:02,773 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:04,608 INFO Connection check task start

2025-08-02 00:14:04,608 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:04,608 INFO Out dated connection ,size=0

2025-08-02 00:14:04,608 INFO Connection check task end

2025-08-02 00:14:05,779 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:07,612 INFO Connection check task start

2025-08-02 00:14:07,612 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:07,612 INFO Out dated connection ,size=0

2025-08-02 00:14:07,612 INFO Connection check task end

2025-08-02 00:14:08,783 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:10,622 INFO Connection check task start

2025-08-02 00:14:10,623 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:10,623 INFO Out dated connection ,size=0

2025-08-02 00:14:10,623 INFO Connection check task end

2025-08-02 00:14:11,789 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:13,630 INFO Connection check task start

2025-08-02 00:14:13,630 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:13,630 INFO Out dated connection ,size=0

2025-08-02 00:14:13,631 INFO Connection check task end

2025-08-02 00:14:14,798 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:16,638 INFO Connection check task start

2025-08-02 00:14:16,638 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:16,638 INFO Out dated connection ,size=0

2025-08-02 00:14:16,638 INFO Connection check task end

2025-08-02 00:14:17,799 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:19,643 INFO Connection check task start

2025-08-02 00:14:19,644 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:19,644 INFO Out dated connection ,size=0

2025-08-02 00:14:19,644 INFO Connection check task end

2025-08-02 00:14:20,799 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:22,648 INFO Connection check task start

2025-08-02 00:14:22,648 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:22,648 INFO Out dated connection ,size=0

2025-08-02 00:14:22,648 INFO Connection check task end

2025-08-02 00:14:23,800 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:25,651 INFO Connection check task start

2025-08-02 00:14:25,651 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:25,651 INFO Out dated connection ,size=0

2025-08-02 00:14:25,651 INFO Connection check task end

2025-08-02 00:14:26,805 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:28,655 INFO Connection check task start

2025-08-02 00:14:28,656 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:28,656 INFO Out dated connection ,size=0

2025-08-02 00:14:28,656 INFO Connection check task end

2025-08-02 00:14:29,809 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:31,657 INFO Connection check task start

2025-08-02 00:14:31,657 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:31,657 INFO Out dated connection ,size=0

2025-08-02 00:14:31,657 INFO Connection check task end

2025-08-02 00:14:32,814 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:34,662 INFO Connection check task start

2025-08-02 00:14:34,663 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:34,663 INFO Out dated connection ,size=0

2025-08-02 00:14:34,663 INFO Connection check task end

2025-08-02 00:14:35,819 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:37,665 INFO Connection check task start

2025-08-02 00:14:37,665 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:37,665 INFO Out dated connection ,size=0

2025-08-02 00:14:37,665 INFO Connection check task end

2025-08-02 00:14:38,822 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:40,669 INFO Connection check task start

2025-08-02 00:14:40,670 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:40,670 INFO Out dated connection ,size=0

2025-08-02 00:14:40,670 INFO Connection check task end

2025-08-02 00:14:41,825 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:43,673 INFO Connection check task start

2025-08-02 00:14:43,673 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:43,673 INFO Out dated connection ,size=0

2025-08-02 00:14:43,673 INFO Connection check task end

2025-08-02 00:14:44,830 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:46,676 INFO Connection check task start

2025-08-02 00:14:46,676 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:46,676 INFO Out dated connection ,size=0

2025-08-02 00:14:46,676 INFO Connection check task end

2025-08-02 00:14:47,835 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:49,680 INFO Connection check task start

2025-08-02 00:14:49,680 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:49,680 INFO Out dated connection ,size=0

2025-08-02 00:14:49,680 INFO Connection check task end

2025-08-02 00:14:50,840 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:52,687 INFO Connection check task start

2025-08-02 00:14:52,687 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:52,687 INFO Out dated connection ,size=0

2025-08-02 00:14:52,687 INFO Connection check task end

2025-08-02 00:14:53,842 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:55,692 INFO Connection check task start

2025-08-02 00:14:55,693 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:55,693 INFO Out dated connection ,size=0

2025-08-02 00:14:55,693 INFO Connection check task end

2025-08-02 00:14:56,848 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:14:58,695 INFO Connection check task start

2025-08-02 00:14:58,695 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:14:58,695 INFO Out dated connection ,size=0

2025-08-02 00:14:58,695 INFO Connection check task end

2025-08-02 00:14:59,851 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:01,700 INFO Connection check task start

2025-08-02 00:15:01,701 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:01,701 INFO Out dated connection ,size=0

2025-08-02 00:15:01,701 INFO Connection check task end

2025-08-02 00:15:02,855 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:04,703 INFO Connection check task start

2025-08-02 00:15:04,703 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:04,703 INFO Out dated connection ,size=0

2025-08-02 00:15:04,703 INFO Connection check task end

2025-08-02 00:15:05,858 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:07,707 INFO Connection check task start

2025-08-02 00:15:07,707 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:07,708 INFO Out dated connection ,size=0

2025-08-02 00:15:07,708 INFO Connection check task end

2025-08-02 00:15:08,863 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:10,709 INFO Connection check task start

2025-08-02 00:15:10,710 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:10,710 INFO Out dated connection ,size=0

2025-08-02 00:15:10,710 INFO Connection check task end

2025-08-02 00:15:11,866 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:13,715 INFO Connection check task start

2025-08-02 00:15:13,715 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:13,715 INFO Out dated connection ,size=0

2025-08-02 00:15:13,715 INFO Connection check task end

2025-08-02 00:15:14,866 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:16,718 INFO Connection check task start

2025-08-02 00:15:16,718 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:16,718 INFO Out dated connection ,size=0

2025-08-02 00:15:16,718 INFO Connection check task end

2025-08-02 00:15:17,872 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:19,722 INFO Connection check task start

2025-08-02 00:15:19,722 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:19,722 INFO Out dated connection ,size=0

2025-08-02 00:15:19,722 INFO Connection check task end

2025-08-02 00:15:20,881 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:22,727 INFO Connection check task start

2025-08-02 00:15:22,728 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:22,728 INFO Out dated connection ,size=0

2025-08-02 00:15:22,728 INFO Connection check task end

2025-08-02 00:15:23,887 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:25,729 INFO Connection check task start

2025-08-02 00:15:25,729 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:25,729 INFO Out dated connection ,size=0

2025-08-02 00:15:25,729 INFO Connection check task end

2025-08-02 00:15:26,888 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:28,733 INFO Connection check task start

2025-08-02 00:15:28,733 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:28,733 INFO Out dated connection ,size=0

2025-08-02 00:15:28,733 INFO Connection check task end

2025-08-02 00:15:29,893 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:31,738 INFO Connection check task start

2025-08-02 00:15:31,739 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:31,739 INFO Out dated connection ,size=0

2025-08-02 00:15:31,739 INFO Connection check task end

2025-08-02 00:15:32,899 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:34,739 INFO Connection check task start

2025-08-02 00:15:34,740 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:34,740 INFO Out dated connection ,size=0

2025-08-02 00:15:34,740 INFO Connection check task end

2025-08-02 00:15:35,900 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:37,741 INFO Connection check task start

2025-08-02 00:15:37,742 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:37,742 INFO Out dated connection ,size=0

2025-08-02 00:15:37,742 INFO Connection check task end

2025-08-02 00:15:38,904 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:40,746 INFO Connection check task start

2025-08-02 00:15:40,746 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:40,747 INFO Out dated connection ,size=0

2025-08-02 00:15:40,747 INFO Connection check task end

2025-08-02 00:15:41,907 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:43,750 INFO Connection check task start

2025-08-02 00:15:43,750 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:43,750 INFO Out dated connection ,size=0

2025-08-02 00:15:43,750 INFO Connection check task end

2025-08-02 00:15:44,908 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:46,754 INFO Connection check task start

2025-08-02 00:15:46,755 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:46,755 INFO Out dated connection ,size=0

2025-08-02 00:15:46,755 INFO Connection check task end

2025-08-02 00:15:47,914 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:49,756 INFO Connection check task start

2025-08-02 00:15:49,756 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:49,756 INFO Out dated connection ,size=0

2025-08-02 00:15:49,757 INFO Connection check task end

2025-08-02 00:15:50,919 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:52,762 INFO Connection check task start

2025-08-02 00:15:52,762 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:52,762 INFO Out dated connection ,size=0

2025-08-02 00:15:52,762 INFO Connection check task end

2025-08-02 00:15:53,923 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:55,767 INFO Connection check task start

2025-08-02 00:15:55,768 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:55,768 INFO Out dated connection ,size=0

2025-08-02 00:15:55,768 INFO Connection check task end

2025-08-02 00:15:56,926 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:15:58,773 INFO Connection check task start

2025-08-02 00:15:58,773 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:15:58,774 INFO Out dated connection ,size=0

2025-08-02 00:15:58,774 INFO Connection check task end

2025-08-02 00:15:59,930 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:01,776 INFO Connection check task start

2025-08-02 00:16:01,777 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:01,777 INFO Out dated connection ,size=0

2025-08-02 00:16:01,777 INFO Connection check task end

2025-08-02 00:16:02,933 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:04,782 INFO Connection check task start

2025-08-02 00:16:04,782 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:04,782 INFO Out dated connection ,size=0

2025-08-02 00:16:04,782 INFO Connection check task end

2025-08-02 00:16:05,939 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:07,787 INFO Connection check task start

2025-08-02 00:16:07,788 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:07,788 INFO Out dated connection ,size=0

2025-08-02 00:16:07,788 INFO Connection check task end

2025-08-02 00:16:08,944 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:10,793 INFO Connection check task start

2025-08-02 00:16:10,793 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:10,793 INFO Out dated connection ,size=0

2025-08-02 00:16:10,793 INFO Connection check task end

2025-08-02 00:16:11,946 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:13,794 INFO Connection check task start

2025-08-02 00:16:13,794 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:13,794 INFO Out dated connection ,size=0

2025-08-02 00:16:13,794 INFO Connection check task end

2025-08-02 00:16:14,947 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:16,799 INFO Connection check task start

2025-08-02 00:16:16,799 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:16,799 INFO Out dated connection ,size=0

2025-08-02 00:16:16,799 INFO Connection check task end

2025-08-02 00:16:17,953 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:19,801 INFO Connection check task start

2025-08-02 00:16:19,801 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:19,801 INFO Out dated connection ,size=0

2025-08-02 00:16:19,801 INFO Connection check task end

2025-08-02 00:16:20,850 INFO [capacityManagement] start correct usage

2025-08-02 00:16:20,850 INFO [MapperManager] findMapper dataSource: mysql, tableName: group_capacity

2025-08-02 00:16:20,852 WARN clearConfigHistory start

2025-08-02 00:16:20,853 WARN clearConfigHistory, getBeforeStamp:2025-07-03 00:16:20.0, pageSize:1000

2025-08-02 00:16:20,853 INFO [MapperManager] findMapper dataSource: mysql, tableName: his_config_info

2025-08-02 00:16:20,853 INFO [MapperManager] findMapper dataSource: mysql, tableName: tenant_capacity

2025-08-02 00:16:20,855 INFO [capacityManagement] end correct usage, cost: 0.004905333s

2025-08-02 00:16:20,956 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:22,806 INFO Connection check task start

2025-08-02 00:16:22,806 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:22,806 INFO Out dated connection ,size=0

2025-08-02 00:16:22,806 INFO Connection check task end

2025-08-02 00:16:23,961 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:25,808 INFO Connection check task start

2025-08-02 00:16:25,808 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:25,809 INFO Out dated connection ,size=0

2025-08-02 00:16:25,809 INFO Connection check task end

2025-08-02 00:16:26,967 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:28,811 INFO Connection check task start

2025-08-02 00:16:28,811 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:28,811 INFO Out dated connection ,size=0

2025-08-02 00:16:28,811 INFO Connection check task end

2025-08-02 00:16:29,972 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:31,816 INFO Connection check task start

2025-08-02 00:16:31,817 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:31,817 INFO Out dated connection ,size=0

2025-08-02 00:16:31,817 INFO Connection check task end

2025-08-02 00:16:32,978 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:34,819 INFO Connection check task start

2025-08-02 00:16:34,820 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:34,820 INFO Out dated connection ,size=0

2025-08-02 00:16:34,820 INFO Connection check task end

2025-08-02 00:16:35,982 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:37,821 INFO Connection check task start

2025-08-02 00:16:37,821 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:37,821 INFO Out dated connection ,size=0

2025-08-02 00:16:37,821 INFO Connection check task end

2025-08-02 00:16:38,988 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:40,826 INFO Connection check task start

2025-08-02 00:16:40,827 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:40,827 INFO Out dated connection ,size=0

2025-08-02 00:16:40,827 INFO Connection check task end

2025-08-02 00:16:41,990 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:43,832 INFO Connection check task start

2025-08-02 00:16:43,833 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:43,833 INFO Out dated connection ,size=0

2025-08-02 00:16:43,833 INFO Connection check task end

2025-08-02 00:16:44,996 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:46,838 INFO Connection check task start

2025-08-02 00:16:46,838 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:46,838 INFO Out dated connection ,size=0

2025-08-02 00:16:46,838 INFO Connection check task end

2025-08-02 00:16:47,998 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:49,840 INFO Connection check task start

2025-08-02 00:16:49,840 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:49,840 INFO Out dated connection ,size=0

2025-08-02 00:16:49,840 INFO Connection check task end

2025-08-02 00:16:51,003 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:52,845 INFO Connection check task start

2025-08-02 00:16:52,846 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:52,846 INFO Out dated connection ,size=0

2025-08-02 00:16:52,846 INFO Connection check task end

2025-08-02 00:16:54,008 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:55,849 INFO Connection check task start

2025-08-02 00:16:55,850 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:55,850 INFO Out dated connection ,size=0

2025-08-02 00:16:55,850 INFO Connection check task end

2025-08-02 00:16:57,012 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:16:58,851 INFO Connection check task start

2025-08-02 00:16:58,852 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:16:58,852 INFO Out dated connection ,size=0

2025-08-02 00:16:58,852 INFO Connection check task end

2025-08-02 00:17:00,015 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:01,857 INFO Connection check task start

2025-08-02 00:17:01,857 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:01,857 INFO Out dated connection ,size=0

2025-08-02 00:17:01,857 INFO Connection check task end

2025-08-02 00:17:03,021 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:04,862 INFO Connection check task start

2025-08-02 00:17:04,862 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:04,863 INFO Out dated connection ,size=0

2025-08-02 00:17:04,863 INFO Connection check task end

2025-08-02 00:17:06,025 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:07,868 INFO Connection check task start

2025-08-02 00:17:07,868 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:07,868 INFO Out dated connection ,size=0

2025-08-02 00:17:07,868 INFO Connection check task end

2025-08-02 00:17:09,028 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:10,870 INFO Connection check task start

2025-08-02 00:17:10,870 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:10,870 INFO Out dated connection ,size=0

2025-08-02 00:17:10,870 INFO Connection check task end

2025-08-02 00:17:12,033 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:13,875 INFO Connection check task start

2025-08-02 00:17:13,876 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:13,876 INFO Out dated connection ,size=0

2025-08-02 00:17:13,876 INFO Connection check task end

2025-08-02 00:17:15,038 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:16,881 INFO Connection check task start

2025-08-02 00:17:16,881 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:16,881 INFO Out dated connection ,size=0

2025-08-02 00:17:16,881 INFO Connection check task end

2025-08-02 00:17:18,040 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:19,883 INFO Connection check task start

2025-08-02 00:17:19,884 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:19,884 INFO Out dated connection ,size=0

2025-08-02 00:17:19,884 INFO Connection check task end

2025-08-02 00:17:21,042 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:22,888 INFO Connection check task start

2025-08-02 00:17:22,888 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:22,888 INFO Out dated connection ,size=0

2025-08-02 00:17:22,888 INFO Connection check task end

2025-08-02 00:17:24,043 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:25,890 INFO Connection check task start

2025-08-02 00:17:25,890 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:25,890 INFO Out dated connection ,size=0

2025-08-02 00:17:25,890 INFO Connection check task end

2025-08-02 00:17:27,046 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:28,892 INFO Connection check task start

2025-08-02 00:17:28,892 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:28,892 INFO Out dated connection ,size=0

2025-08-02 00:17:28,893 INFO Connection check task end

2025-08-02 00:17:30,050 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:31,895 INFO Connection check task start

2025-08-02 00:17:31,896 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:31,896 INFO Out dated connection ,size=0

2025-08-02 00:17:31,896 INFO Connection check task end

2025-08-02 00:17:33,059 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:34,897 INFO Connection check task start

2025-08-02 00:17:34,897 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:34,897 INFO Out dated connection ,size=0

2025-08-02 00:17:34,897 INFO Connection check task end

2025-08-02 00:17:36,069 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:37,899 INFO Connection check task start

2025-08-02 00:17:37,900 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:37,900 INFO Out dated connection ,size=0

2025-08-02 00:17:37,900 INFO Connection check task end

2025-08-02 00:17:39,074 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:40,905 INFO Connection check task start

2025-08-02 00:17:40,905 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:40,905 INFO Out dated connection ,size=0

2025-08-02 00:17:40,906 INFO Connection check task end

2025-08-02 00:17:42,076 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:43,908 INFO Connection check task start

2025-08-02 00:17:43,909 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:43,909 INFO Out dated connection ,size=0

2025-08-02 00:17:43,909 INFO Connection check task end

2025-08-02 00:17:45,081 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:46,915 INFO Connection check task start

2025-08-02 00:17:46,915 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:46,915 INFO Out dated connection ,size=0

2025-08-02 00:17:46,915 INFO Connection check task end

2025-08-02 00:17:48,082 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:49,919 INFO Connection check task start

2025-08-02 00:17:49,919 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:49,920 INFO Out dated connection ,size=0

2025-08-02 00:17:49,920 INFO Connection check task end

2025-08-02 00:17:51,083 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:52,924 INFO Connection check task start

2025-08-02 00:17:52,925 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:52,925 INFO Out dated connection ,size=0

2025-08-02 00:17:52,925 INFO Connection check task end

2025-08-02 00:17:54,087 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:55,928 INFO Connection check task start

2025-08-02 00:17:55,928 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:55,928 INFO Out dated connection ,size=0

2025-08-02 00:17:55,928 INFO Connection check task end

2025-08-02 00:17:57,092 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:17:58,933 INFO Connection check task start

2025-08-02 00:17:58,933 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:17:58,933 INFO Out dated connection ,size=0

2025-08-02 00:17:58,934 INFO Connection check task end

2025-08-02 00:18:00,095 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:01,937 INFO Connection check task start

2025-08-02 00:18:01,938 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:01,938 INFO Out dated connection ,size=0

2025-08-02 00:18:01,938 INFO Connection check task end

2025-08-02 00:18:03,098 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:04,942 INFO Connection check task start

2025-08-02 00:18:04,942 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:04,942 INFO Out dated connection ,size=0

2025-08-02 00:18:04,942 INFO Connection check task end

2025-08-02 00:18:06,107 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:07,947 INFO Connection check task start

2025-08-02 00:18:07,948 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:07,948 INFO Out dated connection ,size=0

2025-08-02 00:18:07,948 INFO Connection check task end

2025-08-02 00:18:09,112 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:10,949 INFO Connection check task start

2025-08-02 00:18:10,949 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:10,950 INFO Out dated connection ,size=0

2025-08-02 00:18:10,950 INFO Connection check task end

2025-08-02 00:18:12,114 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:13,953 INFO Connection check task start

2025-08-02 00:18:13,953 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:13,953 INFO Out dated connection ,size=0

2025-08-02 00:18:13,954 INFO Connection check task end

2025-08-02 00:18:15,117 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:16,954 INFO Connection check task start

2025-08-02 00:18:16,954 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:16,955 INFO Out dated connection ,size=0

2025-08-02 00:18:16,955 INFO Connection check task end

2025-08-02 00:18:18,129 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:19,956 INFO Connection check task start

2025-08-02 00:18:19,956 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:19,956 INFO Out dated connection ,size=0

2025-08-02 00:18:19,956 INFO Connection check task end

2025-08-02 00:18:21,131 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:22,958 INFO Connection check task start

2025-08-02 00:18:22,959 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:22,959 INFO Out dated connection ,size=0

2025-08-02 00:18:22,959 INFO Connection check task end

2025-08-02 00:18:24,134 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:25,962 INFO Connection check task start

2025-08-02 00:18:25,962 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:25,962 INFO Out dated connection ,size=0

2025-08-02 00:18:25,962 INFO Connection check task end

2025-08-02 00:18:27,137 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:28,966 INFO Connection check task start

2025-08-02 00:18:28,967 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:28,967 INFO Out dated connection ,size=0

2025-08-02 00:18:28,967 INFO Connection check task end

2025-08-02 00:18:30,140 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:31,972 INFO Connection check task start

2025-08-02 00:18:31,972 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:31,972 INFO Out dated connection ,size=0

2025-08-02 00:18:31,972 INFO Connection check task end

2025-08-02 00:18:33,141 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:34,975 INFO Connection check task start

2025-08-02 00:18:34,975 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:34,976 INFO Out dated connection ,size=0

2025-08-02 00:18:34,976 INFO Connection check task end

2025-08-02 00:18:36,146 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:37,980 INFO Connection check task start

2025-08-02 00:18:37,981 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:37,981 INFO Out dated connection ,size=0

2025-08-02 00:18:37,981 INFO Connection check task end

2025-08-02 00:18:39,149 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:40,985 INFO Connection check task start

2025-08-02 00:18:40,985 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:40,985 INFO Out dated connection ,size=0

2025-08-02 00:18:40,985 INFO Connection check task end

2025-08-02 00:18:42,150 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:43,989 INFO Connection check task start

2025-08-02 00:18:43,990 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:43,990 INFO Out dated connection ,size=0

2025-08-02 00:18:43,990 INFO Connection check task end

2025-08-02 00:18:45,150 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:46,992 INFO Connection check task start

2025-08-02 00:18:46,992 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:46,992 INFO Out dated connection ,size=0

2025-08-02 00:18:46,992 INFO Connection check task end

2025-08-02 00:18:48,153 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:49,995 INFO Connection check task start

2025-08-02 00:18:49,996 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:49,996 INFO Out dated connection ,size=0

2025-08-02 00:18:49,996 INFO Connection check task end

2025-08-02 00:18:51,158 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:53,001 INFO Connection check task start

2025-08-02 00:18:53,001 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:53,001 INFO Out dated connection ,size=0

2025-08-02 00:18:53,001 INFO Connection check task end

2025-08-02 00:18:54,162 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:56,003 INFO Connection check task start

2025-08-02 00:18:56,003 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:56,004 INFO Out dated connection ,size=0

2025-08-02 00:18:56,004 INFO Connection check task end

2025-08-02 00:18:57,168 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:18:59,006 INFO Connection check task start

2025-08-02 00:18:59,006 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:18:59,006 INFO Out dated connection ,size=0

2025-08-02 00:18:59,006 INFO Connection check task end

2025-08-02 00:19:00,173 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:02,008 INFO Connection check task start

2025-08-02 00:19:02,009 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:02,009 INFO Out dated connection ,size=0

2025-08-02 00:19:02,009 INFO Connection check task end

2025-08-02 00:19:03,174 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:05,009 INFO Connection check task start

2025-08-02 00:19:05,010 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:05,010 INFO Out dated connection ,size=0

2025-08-02 00:19:05,010 INFO Connection check task end

2025-08-02 00:19:06,179 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:08,015 INFO Connection check task start

2025-08-02 00:19:08,015 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:08,015 INFO Out dated connection ,size=0

2025-08-02 00:19:08,015 INFO Connection check task end

2025-08-02 00:19:09,181 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:11,016 INFO Connection check task start

2025-08-02 00:19:11,016 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:11,016 INFO Out dated connection ,size=0

2025-08-02 00:19:11,016 INFO Connection check task end

2025-08-02 00:19:12,185 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:14,022 INFO Connection check task start

2025-08-02 00:19:14,022 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:14,022 INFO Out dated connection ,size=0

2025-08-02 00:19:14,022 INFO Connection check task end

2025-08-02 00:19:15,189 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:17,027 INFO Connection check task start

2025-08-02 00:19:17,027 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:17,027 INFO Out dated connection ,size=0

2025-08-02 00:19:17,027 INFO Connection check task end

2025-08-02 00:19:18,192 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:20,032 INFO Connection check task start

2025-08-02 00:19:20,033 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:20,033 INFO Out dated connection ,size=0

2025-08-02 00:19:20,033 INFO Connection check task end

2025-08-02 00:19:21,195 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:23,037 INFO Connection check task start

2025-08-02 00:19:23,038 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:23,038 INFO Out dated connection ,size=0

2025-08-02 00:19:23,038 INFO Connection check task end

2025-08-02 00:19:24,196 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:26,039 INFO Connection check task start

2025-08-02 00:19:26,040 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:26,040 INFO Out dated connection ,size=0

2025-08-02 00:19:26,040 INFO Connection check task end

2025-08-02 00:19:27,198 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:29,045 INFO Connection check task start

2025-08-02 00:19:29,045 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:29,045 INFO Out dated connection ,size=0

2025-08-02 00:19:29,045 INFO Connection check task end

2025-08-02 00:19:30,200 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:32,046 INFO Connection check task start

2025-08-02 00:19:32,047 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:32,047 INFO Out dated connection ,size=0

2025-08-02 00:19:32,047 INFO Connection check task end

2025-08-02 00:19:33,206 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:35,050 INFO Connection check task start

2025-08-02 00:19:35,051 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:35,051 INFO Out dated connection ,size=0

2025-08-02 00:19:35,051 INFO Connection check task end

2025-08-02 00:19:36,210 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:38,055 INFO Connection check task start

2025-08-02 00:19:38,055 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:38,056 INFO Out dated connection ,size=0

2025-08-02 00:19:38,056 INFO Connection check task end

2025-08-02 00:19:39,214 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:41,061 INFO Connection check task start

2025-08-02 00:19:41,061 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:41,061 INFO Out dated connection ,size=0

2025-08-02 00:19:41,061 INFO Connection check task end

2025-08-02 00:19:42,216 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:44,066 INFO Connection check task start

2025-08-02 00:19:44,066 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:44,066 INFO Out dated connection ,size=0

2025-08-02 00:19:44,067 INFO Connection check task end

2025-08-02 00:19:45,220 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:47,067 INFO Connection check task start

2025-08-02 00:19:47,068 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:47,068 INFO Out dated connection ,size=0

2025-08-02 00:19:47,068 INFO Connection check task end

2025-08-02 00:19:48,223 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:50,069 INFO Connection check task start

2025-08-02 00:19:50,069 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:50,069 INFO Out dated connection ,size=0

2025-08-02 00:19:50,069 INFO Connection check task end

2025-08-02 00:19:51,225 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:53,073 INFO Connection check task start

2025-08-02 00:19:53,073 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:53,073 INFO Out dated connection ,size=0

2025-08-02 00:19:53,073 INFO Connection check task end

2025-08-02 00:19:54,226 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:56,076 INFO Connection check task start

2025-08-02 00:19:56,076 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:56,076 INFO Out dated connection ,size=0

2025-08-02 00:19:56,076 INFO Connection check task end

2025-08-02 00:19:57,229 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:19:59,077 INFO Connection check task start

2025-08-02 00:19:59,077 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:19:59,077 INFO Out dated connection ,size=0

2025-08-02 00:19:59,077 INFO Connection check task end

2025-08-02 00:20:00,229 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:02,082 INFO Connection check task start

2025-08-02 00:20:02,082 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:02,083 INFO Out dated connection ,size=0

2025-08-02 00:20:02,083 INFO Connection check task end

2025-08-02 00:20:03,233 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:05,087 INFO Connection check task start

2025-08-02 00:20:05,087 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:05,087 INFO Out dated connection ,size=0

2025-08-02 00:20:05,087 INFO Connection check task end

2025-08-02 00:20:06,236 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:08,090 INFO Connection check task start

2025-08-02 00:20:08,091 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:08,091 INFO Out dated connection ,size=0

2025-08-02 00:20:08,091 INFO Connection check task end

2025-08-02 00:20:09,238 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:11,093 INFO Connection check task start

2025-08-02 00:20:11,093 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:11,093 INFO Out dated connection ,size=0

2025-08-02 00:20:11,093 INFO Connection check task end

2025-08-02 00:20:12,239 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:14,096 INFO Connection check task start

2025-08-02 00:20:14,097 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:14,097 INFO Out dated connection ,size=0

2025-08-02 00:20:14,097 INFO Connection check task end

2025-08-02 00:20:15,244 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:17,100 INFO Connection check task start

2025-08-02 00:20:17,101 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:17,101 INFO Out dated connection ,size=0

2025-08-02 00:20:17,101 INFO Connection check task end

2025-08-02 00:20:18,249 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:20,103 INFO Connection check task start

2025-08-02 00:20:20,103 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:20,103 INFO Out dated connection ,size=0

2025-08-02 00:20:20,103 INFO Connection check task end

2025-08-02 00:20:21,253 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:23,104 INFO Connection check task start

2025-08-02 00:20:23,105 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:23,105 INFO Out dated connection ,size=0

2025-08-02 00:20:23,105 INFO Connection check task end

2025-08-02 00:20:24,254 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:26,110 INFO Connection check task start

2025-08-02 00:20:26,110 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:26,111 INFO Out dated connection ,size=0

2025-08-02 00:20:26,111 INFO Connection check task end

2025-08-02 00:20:27,255 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:29,113 INFO Connection check task start

2025-08-02 00:20:29,113 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:29,113 INFO Out dated connection ,size=0

2025-08-02 00:20:29,113 INFO Connection check task end

2025-08-02 00:20:30,258 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:32,117 INFO Connection check task start

2025-08-02 00:20:32,118 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:32,118 INFO Out dated connection ,size=0

2025-08-02 00:20:32,118 INFO Connection check task end

2025-08-02 00:20:33,264 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:35,122 INFO Connection check task start

2025-08-02 00:20:35,123 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:35,123 INFO Out dated connection ,size=0

2025-08-02 00:20:35,123 INFO Connection check task end

2025-08-02 00:20:36,269 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:38,128 INFO Connection check task start

2025-08-02 00:20:38,128 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:38,128 INFO Out dated connection ,size=0

2025-08-02 00:20:38,128 INFO Connection check task end

2025-08-02 00:20:39,270 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:41,130 INFO Connection check task start

2025-08-02 00:20:41,130 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:41,130 INFO Out dated connection ,size=0

2025-08-02 00:20:41,130 INFO Connection check task end

2025-08-02 00:20:42,275 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:44,131 INFO Connection check task start

2025-08-02 00:20:44,131 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:44,131 INFO Out dated connection ,size=0

2025-08-02 00:20:44,131 INFO Connection check task end

2025-08-02 00:20:45,281 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:20:47,132 INFO Connection check task start

2025-08-02 00:20:47,133 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:20:47,133 INFO Out dated connection ,size=0

2025-08-02 00:20:47,133 INFO Connection check task end

2025-08-02 00:20:47,585 INFO [1754055759425_127.0.0.1_64131]Connection unregistered successfully. 

2025-08-02 00:20:47,600 INFO [1754062394721_127.0.0.1_59464]Connection unregistered successfully. 

2025-08-02 00:20:47,600 INFO [1754062475327_127.0.0.1_59592]Connection unregistered successfully. 

2025-08-02 00:20:47,606 INFO [1754063129068_127.0.0.1_60434]Connection unregistered successfully. 

2025-08-02 00:20:47,615 INFO [1754064396786_127.0.0.1_63717]Connection unregistered successfully. 

2025-08-02 00:20:48,286 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:20:49,696 INFO [1754064664870_127.0.0.1_65169]Connection unregistered successfully. 

2025-08-02 00:20:50,138 INFO Connection check task start

2025-08-02 00:20:50,138 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:20:50,144 INFO Out dated connection ,size=0

2025-08-02 00:20:50,144 INFO Connection check task end

2025-08-02 00:20:51,291 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:20:53,148 INFO Connection check task start

2025-08-02 00:20:53,148 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:20:53,149 INFO Out dated connection ,size=0

2025-08-02 00:20:53,149 INFO Connection check task end

2025-08-02 00:20:54,293 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:20:56,152 INFO Connection check task start

2025-08-02 00:20:56,153 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:20:56,153 INFO Out dated connection ,size=0

2025-08-02 00:20:56,153 INFO Connection check task end

2025-08-02 00:20:57,297 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:20:59,158 INFO Connection check task start

2025-08-02 00:20:59,159 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:20:59,159 INFO Out dated connection ,size=0

2025-08-02 00:20:59,159 INFO Connection check task end

2025-08-02 00:21:00,302 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:02,164 INFO Connection check task start

2025-08-02 00:21:02,164 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:02,165 INFO Out dated connection ,size=0

2025-08-02 00:21:02,165 INFO Connection check task end

2025-08-02 00:21:03,308 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:05,169 INFO Connection check task start

2025-08-02 00:21:05,170 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:05,170 INFO Out dated connection ,size=0

2025-08-02 00:21:05,170 INFO Connection check task end

2025-08-02 00:21:06,313 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:08,173 INFO Connection check task start

2025-08-02 00:21:08,174 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:08,174 INFO Out dated connection ,size=0

2025-08-02 00:21:08,174 INFO Connection check task end

2025-08-02 00:21:09,318 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:11,179 INFO Connection check task start

2025-08-02 00:21:11,179 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:11,179 INFO Out dated connection ,size=0

2025-08-02 00:21:11,180 INFO Connection check task end

2025-08-02 00:21:12,320 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:14,182 INFO Connection check task start

2025-08-02 00:21:14,183 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:14,183 INFO Out dated connection ,size=0

2025-08-02 00:21:14,183 INFO Connection check task end

2025-08-02 00:21:15,325 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:17,188 INFO Connection check task start

2025-08-02 00:21:17,188 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:17,188 INFO Out dated connection ,size=0

2025-08-02 00:21:17,188 INFO Connection check task end

2025-08-02 00:21:18,328 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:20,192 INFO Connection check task start

2025-08-02 00:21:20,193 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:20,193 INFO Out dated connection ,size=0

2025-08-02 00:21:20,193 INFO Connection check task end

2025-08-02 00:21:21,333 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:23,197 INFO Connection check task start

2025-08-02 00:21:23,198 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:23,198 INFO Out dated connection ,size=0

2025-08-02 00:21:23,198 INFO Connection check task end

2025-08-02 00:21:24,338 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:26,203 INFO Connection check task start

2025-08-02 00:21:26,203 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:26,203 INFO Out dated connection ,size=0

2025-08-02 00:21:26,203 INFO Connection check task end

2025-08-02 00:21:27,340 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:29,208 INFO Connection check task start

2025-08-02 00:21:29,208 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:29,208 INFO Out dated connection ,size=0

2025-08-02 00:21:29,209 INFO Connection check task end

2025-08-02 00:21:30,345 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:32,211 INFO Connection check task start

2025-08-02 00:21:32,211 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:32,211 INFO Out dated connection ,size=0

2025-08-02 00:21:32,211 INFO Connection check task end

2025-08-02 00:21:33,346 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:35,212 INFO Connection check task start

2025-08-02 00:21:35,213 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:35,213 INFO Out dated connection ,size=0

2025-08-02 00:21:35,213 INFO Connection check task end

2025-08-02 00:21:36,350 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:38,218 INFO Connection check task start

2025-08-02 00:21:38,218 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:38,218 INFO Out dated connection ,size=0

2025-08-02 00:21:38,218 INFO Connection check task end

2025-08-02 00:21:39,352 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:41,223 INFO Connection check task start

2025-08-02 00:21:41,224 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:41,224 INFO Out dated connection ,size=0

2025-08-02 00:21:41,224 INFO Connection check task end

2025-08-02 00:21:42,357 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:44,224 INFO Connection check task start

2025-08-02 00:21:44,225 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:44,225 INFO Out dated connection ,size=0

2025-08-02 00:21:44,225 INFO Connection check task end

2025-08-02 00:21:45,361 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:47,227 INFO Connection check task start

2025-08-02 00:21:47,227 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:47,228 INFO Out dated connection ,size=0

2025-08-02 00:21:47,228 INFO Connection check task end

2025-08-02 00:21:48,364 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:50,232 INFO Connection check task start

2025-08-02 00:21:50,232 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:50,232 INFO Out dated connection ,size=0

2025-08-02 00:21:50,232 INFO Connection check task end

2025-08-02 00:21:51,366 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:53,237 INFO Connection check task start

2025-08-02 00:21:53,238 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:53,238 INFO Out dated connection ,size=0

2025-08-02 00:21:53,238 INFO Connection check task end

2025-08-02 00:21:54,368 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:56,240 INFO Connection check task start

2025-08-02 00:21:56,240 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:56,241 INFO Out dated connection ,size=0

2025-08-02 00:21:56,241 INFO Connection check task end

2025-08-02 00:21:57,373 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:21:59,244 INFO Connection check task start

2025-08-02 00:21:59,244 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:21:59,244 INFO Out dated connection ,size=0

2025-08-02 00:21:59,245 INFO Connection check task end

2025-08-02 00:22:00,379 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:02,250 INFO Connection check task start

2025-08-02 00:22:02,250 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:02,251 INFO Out dated connection ,size=0

2025-08-02 00:22:02,251 INFO Connection check task end

2025-08-02 00:22:03,382 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:05,251 INFO Connection check task start

2025-08-02 00:22:05,251 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:05,252 INFO Out dated connection ,size=0

2025-08-02 00:22:05,252 INFO Connection check task end

2025-08-02 00:22:06,388 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:08,253 INFO Connection check task start

2025-08-02 00:22:08,253 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:08,253 INFO Out dated connection ,size=0

2025-08-02 00:22:08,253 INFO Connection check task end

2025-08-02 00:22:09,391 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:11,256 INFO Connection check task start

2025-08-02 00:22:11,257 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:11,257 INFO Out dated connection ,size=0

2025-08-02 00:22:11,258 INFO Connection check task end

2025-08-02 00:22:12,395 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:14,260 INFO Connection check task start

2025-08-02 00:22:14,261 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:14,261 INFO Out dated connection ,size=0

2025-08-02 00:22:14,261 INFO Connection check task end

2025-08-02 00:22:15,397 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:17,266 INFO Connection check task start

2025-08-02 00:22:17,267 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:17,267 INFO Out dated connection ,size=0

2025-08-02 00:22:17,267 INFO Connection check task end

2025-08-02 00:22:18,400 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:20,270 INFO Connection check task start

2025-08-02 00:22:20,270 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:20,270 INFO Out dated connection ,size=0

2025-08-02 00:22:20,270 INFO Connection check task end

2025-08-02 00:22:21,402 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:23,274 INFO Connection check task start

2025-08-02 00:22:23,275 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:23,275 INFO Out dated connection ,size=0

2025-08-02 00:22:23,275 INFO Connection check task end

2025-08-02 00:22:24,408 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:26,280 INFO Connection check task start

2025-08-02 00:22:26,280 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:26,281 INFO Out dated connection ,size=0

2025-08-02 00:22:26,281 INFO Connection check task end

2025-08-02 00:22:27,412 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:29,283 INFO Connection check task start

2025-08-02 00:22:29,283 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:29,283 INFO Out dated connection ,size=0

2025-08-02 00:22:29,283 INFO Connection check task end

2025-08-02 00:22:30,412 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:32,286 INFO Connection check task start

2025-08-02 00:22:32,287 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:32,287 INFO Out dated connection ,size=0

2025-08-02 00:22:32,287 INFO Connection check task end

2025-08-02 00:22:33,416 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:35,288 INFO Connection check task start

2025-08-02 00:22:35,288 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:35,289 INFO Out dated connection ,size=0

2025-08-02 00:22:35,289 INFO Connection check task end

2025-08-02 00:22:36,419 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:38,291 INFO Connection check task start

2025-08-02 00:22:38,291 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:38,291 INFO Out dated connection ,size=0

2025-08-02 00:22:38,291 INFO Connection check task end

2025-08-02 00:22:39,425 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:41,294 INFO Connection check task start

2025-08-02 00:22:41,295 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:41,295 INFO Out dated connection ,size=0

2025-08-02 00:22:41,295 INFO Connection check task end

2025-08-02 00:22:42,431 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:44,300 INFO Connection check task start

2025-08-02 00:22:44,300 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:44,300 INFO Out dated connection ,size=0

2025-08-02 00:22:44,300 INFO Connection check task end

2025-08-02 00:22:45,436 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:22:47,302 INFO Connection check task start

2025-08-02 00:22:47,303 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:22:47,303 INFO Out dated connection ,size=0

2025-08-02 00:22:47,303 INFO Connection check task end

2025-08-02 00:22:48,056 INFO new connection registered successfully, connectionId = 1754065368019_127.0.0.1_52593,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=52593, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065368019_127.0.0.1_52593', createTime=Sat Aug 02 00:22:48 CST 2025, lastActiveTime=1754065368056, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:22:48,437 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:22:50,306 INFO Connection check task start

2025-08-02 00:22:50,307 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:22:50,307 INFO Out dated connection ,size=0

2025-08-02 00:22:50,307 INFO Connection check task end

2025-08-02 00:22:50,374 INFO new connection registered successfully, connectionId = 1754065370344_127.0.0.1_52608,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=52608, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065370344_127.0.0.1_52608', createTime=Sat Aug 02 00:22:50 CST 2025, lastActiveTime=1754065370374, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:22:51,442 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:22:53,307 INFO Connection check task start

2025-08-02 00:22:53,307 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:22:53,307 INFO Out dated connection ,size=0

2025-08-02 00:22:53,307 INFO Connection check task end

2025-08-02 00:22:54,445 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:22:56,311 INFO Connection check task start

2025-08-02 00:22:56,311 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:22:56,311 INFO Out dated connection ,size=0

2025-08-02 00:22:56,311 INFO Connection check task end

2025-08-02 00:22:57,450 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:22:59,316 INFO Connection check task start

2025-08-02 00:22:59,316 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:22:59,316 INFO Out dated connection ,size=0

2025-08-02 00:22:59,316 INFO Connection check task end

2025-08-02 00:23:00,452 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:02,317 INFO Connection check task start

2025-08-02 00:23:02,317 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:02,317 INFO Out dated connection ,size=0

2025-08-02 00:23:02,317 INFO Connection check task end

2025-08-02 00:23:03,453 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:05,322 INFO Connection check task start

2025-08-02 00:23:05,323 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:05,323 INFO Out dated connection ,size=0

2025-08-02 00:23:05,323 INFO Connection check task end

2025-08-02 00:23:06,454 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:08,328 INFO Connection check task start

2025-08-02 00:23:08,328 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:08,328 INFO Out dated connection ,size=0

2025-08-02 00:23:08,328 INFO Connection check task end

2025-08-02 00:23:09,459 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:11,332 INFO Connection check task start

2025-08-02 00:23:11,333 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:11,333 INFO Out dated connection ,size=0

2025-08-02 00:23:11,333 INFO Connection check task end

2025-08-02 00:23:12,460 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:14,338 INFO Connection check task start

2025-08-02 00:23:14,338 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:14,338 INFO Out dated connection ,size=0

2025-08-02 00:23:14,338 INFO Connection check task end

2025-08-02 00:23:15,466 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:17,343 INFO Connection check task start

2025-08-02 00:23:17,343 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:17,343 INFO Out dated connection ,size=0

2025-08-02 00:23:17,343 INFO Connection check task end

2025-08-02 00:23:18,467 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:20,344 INFO Connection check task start

2025-08-02 00:23:20,344 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:20,344 INFO Out dated connection ,size=0

2025-08-02 00:23:20,344 INFO Connection check task end

2025-08-02 00:23:21,472 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:23,346 INFO Connection check task start

2025-08-02 00:23:23,346 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:23,347 INFO Out dated connection ,size=0

2025-08-02 00:23:23,347 INFO Connection check task end

2025-08-02 00:23:24,477 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:26,352 INFO Connection check task start

2025-08-02 00:23:26,352 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:26,352 INFO Out dated connection ,size=0

2025-08-02 00:23:26,352 INFO Connection check task end

2025-08-02 00:23:27,482 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:29,357 INFO Connection check task start

2025-08-02 00:23:29,357 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:29,357 INFO Out dated connection ,size=0

2025-08-02 00:23:29,357 INFO Connection check task end

2025-08-02 00:23:30,483 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:32,360 INFO Connection check task start

2025-08-02 00:23:32,361 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:32,361 INFO Out dated connection ,size=0

2025-08-02 00:23:32,361 INFO Connection check task end

2025-08-02 00:23:33,486 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:35,363 INFO Connection check task start

2025-08-02 00:23:35,363 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:35,363 INFO Out dated connection ,size=0

2025-08-02 00:23:35,363 INFO Connection check task end

2025-08-02 00:23:36,487 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:38,365 INFO Connection check task start

2025-08-02 00:23:38,365 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:38,365 INFO Out dated connection ,size=0

2025-08-02 00:23:38,365 INFO Connection check task end

2025-08-02 00:23:39,491 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:41,370 INFO Connection check task start

2025-08-02 00:23:41,371 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:41,371 INFO Out dated connection ,size=0

2025-08-02 00:23:41,371 INFO Connection check task end

2025-08-02 00:23:42,494 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:44,373 INFO Connection check task start

2025-08-02 00:23:44,373 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:44,373 INFO Out dated connection ,size=0

2025-08-02 00:23:44,373 INFO Connection check task end

2025-08-02 00:23:45,496 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:47,374 INFO Connection check task start

2025-08-02 00:23:47,374 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:47,374 INFO Out dated connection ,size=0

2025-08-02 00:23:47,374 INFO Connection check task end

2025-08-02 00:23:48,501 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:50,378 INFO Connection check task start

2025-08-02 00:23:50,379 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:50,379 INFO Out dated connection ,size=0

2025-08-02 00:23:50,379 INFO Connection check task end

2025-08-02 00:23:51,507 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:53,381 INFO Connection check task start

2025-08-02 00:23:53,381 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:53,381 INFO Out dated connection ,size=0

2025-08-02 00:23:53,381 INFO Connection check task end

2025-08-02 00:23:54,510 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:56,386 INFO Connection check task start

2025-08-02 00:23:56,387 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:56,387 INFO Out dated connection ,size=0

2025-08-02 00:23:56,387 INFO Connection check task end

2025-08-02 00:23:57,516 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:23:59,392 INFO Connection check task start

2025-08-02 00:23:59,392 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:23:59,393 INFO Out dated connection ,size=0

2025-08-02 00:23:59,393 INFO Connection check task end

2025-08-02 00:24:00,518 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:02,394 INFO Connection check task start

2025-08-02 00:24:02,394 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:02,394 INFO Out dated connection ,size=0

2025-08-02 00:24:02,394 INFO Connection check task end

2025-08-02 00:24:03,523 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:05,395 INFO Connection check task start

2025-08-02 00:24:05,395 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:05,395 INFO Out dated connection ,size=0

2025-08-02 00:24:05,396 INFO Connection check task end

2025-08-02 00:24:06,527 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:08,399 INFO Connection check task start

2025-08-02 00:24:08,400 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:08,400 INFO Out dated connection ,size=0

2025-08-02 00:24:08,400 INFO Connection check task end

2025-08-02 00:24:09,532 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:11,404 INFO Connection check task start

2025-08-02 00:24:11,404 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:11,405 INFO Out dated connection ,size=0

2025-08-02 00:24:11,405 INFO Connection check task end

2025-08-02 00:24:12,537 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:14,410 INFO Connection check task start

2025-08-02 00:24:14,410 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:14,410 INFO Out dated connection ,size=0

2025-08-02 00:24:14,410 INFO Connection check task end

2025-08-02 00:24:15,538 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:17,411 INFO Connection check task start

2025-08-02 00:24:17,412 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:17,412 INFO Out dated connection ,size=0

2025-08-02 00:24:17,412 INFO Connection check task end

2025-08-02 00:24:18,543 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:20,416 INFO Connection check task start

2025-08-02 00:24:20,416 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:20,416 INFO Out dated connection ,size=0

2025-08-02 00:24:20,416 INFO Connection check task end

2025-08-02 00:24:21,548 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:23,420 INFO Connection check task start

2025-08-02 00:24:23,421 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:23,421 INFO Out dated connection ,size=0

2025-08-02 00:24:23,421 INFO Connection check task end

2025-08-02 00:24:24,553 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:26,424 INFO Connection check task start

2025-08-02 00:24:26,425 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:26,425 INFO Out dated connection ,size=0

2025-08-02 00:24:26,425 INFO Connection check task end

2025-08-02 00:24:27,557 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:29,426 INFO Connection check task start

2025-08-02 00:24:29,426 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:29,426 INFO Out dated connection ,size=0

2025-08-02 00:24:29,426 INFO Connection check task end

2025-08-02 00:24:30,563 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:32,428 INFO Connection check task start

2025-08-02 00:24:32,428 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:32,428 INFO Out dated connection ,size=0

2025-08-02 00:24:32,428 INFO Connection check task end

2025-08-02 00:24:33,568 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:35,433 INFO Connection check task start

2025-08-02 00:24:35,433 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:35,433 INFO Out dated connection ,size=0

2025-08-02 00:24:35,433 INFO Connection check task end

2025-08-02 00:24:36,574 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:38,439 INFO Connection check task start

2025-08-02 00:24:38,439 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:38,439 INFO Out dated connection ,size=0

2025-08-02 00:24:38,439 INFO Connection check task end

2025-08-02 00:24:39,579 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:41,440 INFO Connection check task start

2025-08-02 00:24:41,440 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:41,441 INFO Out dated connection ,size=0

2025-08-02 00:24:41,441 INFO Connection check task end

2025-08-02 00:24:42,584 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:44,446 INFO Connection check task start

2025-08-02 00:24:44,446 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:44,446 INFO Out dated connection ,size=0

2025-08-02 00:24:44,446 INFO Connection check task end

2025-08-02 00:24:45,590 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:47,447 INFO Connection check task start

2025-08-02 00:24:47,448 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:47,448 INFO Out dated connection ,size=0

2025-08-02 00:24:47,448 INFO Connection check task end

2025-08-02 00:24:48,591 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:50,452 INFO Connection check task start

2025-08-02 00:24:50,452 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:50,452 INFO Out dated connection ,size=0

2025-08-02 00:24:50,452 INFO Connection check task end

2025-08-02 00:24:51,594 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:53,454 INFO Connection check task start

2025-08-02 00:24:53,455 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:53,455 INFO Out dated connection ,size=0

2025-08-02 00:24:53,455 INFO Connection check task end

2025-08-02 00:24:54,596 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:56,460 INFO Connection check task start

2025-08-02 00:24:56,461 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:56,461 INFO Out dated connection ,size=0

2025-08-02 00:24:56,461 INFO Connection check task end

2025-08-02 00:24:57,602 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:24:59,462 INFO Connection check task start

2025-08-02 00:24:59,462 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:24:59,463 INFO Out dated connection ,size=0

2025-08-02 00:24:59,463 INFO Connection check task end

2025-08-02 00:25:00,607 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:02,468 INFO Connection check task start

2025-08-02 00:25:02,468 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:02,468 INFO Out dated connection ,size=0

2025-08-02 00:25:02,468 INFO Connection check task end

2025-08-02 00:25:03,613 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:05,473 INFO Connection check task start

2025-08-02 00:25:05,474 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:05,474 INFO Out dated connection ,size=0

2025-08-02 00:25:05,474 INFO Connection check task end

2025-08-02 00:25:06,614 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:08,479 INFO Connection check task start

2025-08-02 00:25:08,480 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:08,480 INFO Out dated connection ,size=0

2025-08-02 00:25:08,480 INFO Connection check task end

2025-08-02 00:25:09,620 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:10,893 INFO [1754065368019_127.0.0.1_52593]Connection unregistered successfully. 

2025-08-02 00:25:11,481 INFO Connection check task start

2025-08-02 00:25:11,481 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:25:11,481 INFO Out dated connection ,size=0

2025-08-02 00:25:11,481 INFO Connection check task end

2025-08-02 00:25:12,622 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:25:14,486 INFO Connection check task start

2025-08-02 00:25:14,486 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:25:14,486 INFO Out dated connection ,size=0

2025-08-02 00:25:14,487 INFO Connection check task end

2025-08-02 00:25:15,626 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:25:17,488 INFO Connection check task start

2025-08-02 00:25:17,488 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:25:17,488 INFO Out dated connection ,size=0

2025-08-02 00:25:17,488 INFO Connection check task end

2025-08-02 00:25:17,868 INFO new connection registered successfully, connectionId = 1754065517844_127.0.0.1_53417,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=53417, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065517844_127.0.0.1_53417', createTime=Sat Aug 02 00:25:17 CST 2025, lastActiveTime=1754065517868, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:25:18,630 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:20,492 INFO Connection check task start

2025-08-02 00:25:20,493 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:20,493 INFO Out dated connection ,size=0

2025-08-02 00:25:20,493 INFO Connection check task end

2025-08-02 00:25:21,632 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:23,498 INFO Connection check task start

2025-08-02 00:25:23,498 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:23,498 INFO Out dated connection ,size=0

2025-08-02 00:25:23,498 INFO Connection check task end

2025-08-02 00:25:24,637 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:26,501 INFO Connection check task start

2025-08-02 00:25:26,501 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:26,501 INFO Out dated connection ,size=0

2025-08-02 00:25:26,501 INFO Connection check task end

2025-08-02 00:25:27,638 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:29,504 INFO Connection check task start

2025-08-02 00:25:29,504 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:29,504 INFO Out dated connection ,size=0

2025-08-02 00:25:29,504 INFO Connection check task end

2025-08-02 00:25:30,640 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:32,509 INFO Connection check task start

2025-08-02 00:25:32,509 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:32,509 INFO Out dated connection ,size=0

2025-08-02 00:25:32,509 INFO Connection check task end

2025-08-02 00:25:33,645 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:35,510 INFO Connection check task start

2025-08-02 00:25:35,510 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:35,510 INFO Out dated connection ,size=0

2025-08-02 00:25:35,510 INFO Connection check task end

2025-08-02 00:25:36,646 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:38,514 INFO Connection check task start

2025-08-02 00:25:38,515 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:38,515 INFO Out dated connection ,size=0

2025-08-02 00:25:38,515 INFO Connection check task end

2025-08-02 00:25:39,648 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:41,518 INFO Connection check task start

2025-08-02 00:25:41,518 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:41,518 INFO Out dated connection ,size=0

2025-08-02 00:25:41,518 INFO Connection check task end

2025-08-02 00:25:42,650 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:44,519 INFO Connection check task start

2025-08-02 00:25:44,519 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:44,519 INFO Out dated connection ,size=0

2025-08-02 00:25:44,519 INFO Connection check task end

2025-08-02 00:25:45,655 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:47,523 INFO Connection check task start

2025-08-02 00:25:47,523 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:47,523 INFO Out dated connection ,size=0

2025-08-02 00:25:47,523 INFO Connection check task end

2025-08-02 00:25:48,658 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:50,526 INFO Connection check task start

2025-08-02 00:25:50,526 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:50,526 INFO Out dated connection ,size=0

2025-08-02 00:25:50,526 INFO Connection check task end

2025-08-02 00:25:51,663 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:53,529 INFO Connection check task start

2025-08-02 00:25:53,530 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:53,530 INFO Out dated connection ,size=0

2025-08-02 00:25:53,530 INFO Connection check task end

2025-08-02 00:25:54,664 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:56,531 INFO Connection check task start

2025-08-02 00:25:56,531 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:56,531 INFO Out dated connection ,size=0

2025-08-02 00:25:56,531 INFO Connection check task end

2025-08-02 00:25:57,665 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:25:59,536 INFO Connection check task start

2025-08-02 00:25:59,536 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:25:59,536 INFO Out dated connection ,size=0

2025-08-02 00:25:59,536 INFO Connection check task end

2025-08-02 00:26:00,667 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:02,541 INFO Connection check task start

2025-08-02 00:26:02,541 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:02,541 INFO Out dated connection ,size=0

2025-08-02 00:26:02,541 INFO Connection check task end

2025-08-02 00:26:03,671 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:05,541 INFO Connection check task start

2025-08-02 00:26:05,542 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:05,542 INFO Out dated connection ,size=0

2025-08-02 00:26:05,542 INFO Connection check task end

2025-08-02 00:26:06,675 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:08,543 INFO Connection check task start

2025-08-02 00:26:08,543 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:08,543 INFO Out dated connection ,size=0

2025-08-02 00:26:08,543 INFO Connection check task end

2025-08-02 00:26:09,678 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:11,547 INFO Connection check task start

2025-08-02 00:26:11,547 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:11,547 INFO Out dated connection ,size=0

2025-08-02 00:26:11,548 INFO Connection check task end

2025-08-02 00:26:12,681 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:14,553 INFO Connection check task start

2025-08-02 00:26:14,553 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:14,553 INFO Out dated connection ,size=0

2025-08-02 00:26:14,553 INFO Connection check task end

2025-08-02 00:26:15,686 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:17,554 INFO Connection check task start

2025-08-02 00:26:17,554 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:17,554 INFO Out dated connection ,size=0

2025-08-02 00:26:17,554 INFO Connection check task end

2025-08-02 00:26:18,690 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:20,558 INFO Connection check task start

2025-08-02 00:26:20,558 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:20,558 INFO Out dated connection ,size=0

2025-08-02 00:26:20,558 INFO Connection check task end

2025-08-02 00:26:20,856 INFO [capacityManagement] start correct usage

2025-08-02 00:26:20,856 INFO [MapperManager] findMapper dataSource: mysql, tableName: group_capacity

2025-08-02 00:26:20,856 INFO [MapperManager] findMapper dataSource: mysql, tableName: tenant_capacity

2025-08-02 00:26:20,856 INFO [capacityManagement] end correct usage, cost: 8.29458E-4s

2025-08-02 00:26:20,857 WARN clearConfigHistory start

2025-08-02 00:26:20,857 WARN clearConfigHistory, getBeforeStamp:2025-07-03 00:26:20.0, pageSize:1000

2025-08-02 00:26:20,857 INFO [MapperManager] findMapper dataSource: mysql, tableName: his_config_info

2025-08-02 00:26:21,692 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:23,560 INFO Connection check task start

2025-08-02 00:26:23,561 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:23,561 INFO Out dated connection ,size=0

2025-08-02 00:26:23,561 INFO Connection check task end

2025-08-02 00:26:24,692 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:26,564 INFO Connection check task start

2025-08-02 00:26:26,564 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:26,564 INFO Out dated connection ,size=0

2025-08-02 00:26:26,564 INFO Connection check task end

2025-08-02 00:26:27,697 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:29,566 INFO Connection check task start

2025-08-02 00:26:29,566 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:29,566 INFO Out dated connection ,size=0

2025-08-02 00:26:29,566 INFO Connection check task end

2025-08-02 00:26:30,698 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:32,569 INFO Connection check task start

2025-08-02 00:26:32,569 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:32,569 INFO Out dated connection ,size=0

2025-08-02 00:26:32,569 INFO Connection check task end

2025-08-02 00:26:33,704 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:35,574 INFO Connection check task start

2025-08-02 00:26:35,575 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:35,575 INFO Out dated connection ,size=0

2025-08-02 00:26:35,575 INFO Connection check task end

2025-08-02 00:26:36,708 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:38,580 INFO Connection check task start

2025-08-02 00:26:38,580 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:38,580 INFO Out dated connection ,size=0

2025-08-02 00:26:38,580 INFO Connection check task end

2025-08-02 00:26:39,710 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:41,584 INFO Connection check task start

2025-08-02 00:26:41,585 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:41,585 INFO Out dated connection ,size=0

2025-08-02 00:26:41,585 INFO Connection check task end

2025-08-02 00:26:42,715 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:44,587 INFO Connection check task start

2025-08-02 00:26:44,588 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:44,588 INFO Out dated connection ,size=0

2025-08-02 00:26:44,588 INFO Connection check task end

2025-08-02 00:26:45,716 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:47,593 INFO Connection check task start

2025-08-02 00:26:47,593 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:47,593 INFO Out dated connection ,size=0

2025-08-02 00:26:47,593 INFO Connection check task end

2025-08-02 00:26:48,718 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:50,593 INFO Connection check task start

2025-08-02 00:26:50,593 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:50,593 INFO Out dated connection ,size=0

2025-08-02 00:26:50,594 INFO Connection check task end

2025-08-02 00:26:51,723 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:53,596 INFO Connection check task start

2025-08-02 00:26:53,597 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:53,597 INFO Out dated connection ,size=0

2025-08-02 00:26:53,597 INFO Connection check task end

2025-08-02 00:26:54,725 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:56,599 INFO Connection check task start

2025-08-02 00:26:56,600 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:56,600 INFO Out dated connection ,size=0

2025-08-02 00:26:56,600 INFO Connection check task end

2025-08-02 00:26:57,729 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:26:59,605 INFO Connection check task start

2025-08-02 00:26:59,605 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:26:59,605 INFO Out dated connection ,size=0

2025-08-02 00:26:59,605 INFO Connection check task end

2025-08-02 00:27:00,732 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:02,609 INFO Connection check task start

2025-08-02 00:27:02,609 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:02,609 INFO Out dated connection ,size=0

2025-08-02 00:27:02,609 INFO Connection check task end

2025-08-02 00:27:03,735 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:05,613 INFO Connection check task start

2025-08-02 00:27:05,614 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:05,615 INFO Out dated connection ,size=0

2025-08-02 00:27:05,615 INFO Connection check task end

2025-08-02 00:27:06,741 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:08,619 INFO Connection check task start

2025-08-02 00:27:08,619 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:08,619 INFO Out dated connection ,size=0

2025-08-02 00:27:08,619 INFO Connection check task end

2025-08-02 00:27:09,745 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:11,622 INFO Connection check task start

2025-08-02 00:27:11,622 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:11,622 INFO Out dated connection ,size=0

2025-08-02 00:27:11,622 INFO Connection check task end

2025-08-02 00:27:12,749 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:14,623 INFO Connection check task start

2025-08-02 00:27:14,623 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:14,623 INFO Out dated connection ,size=0

2025-08-02 00:27:14,623 INFO Connection check task end

2025-08-02 00:27:15,751 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:17,626 INFO Connection check task start

2025-08-02 00:27:17,627 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:17,627 INFO Out dated connection ,size=0

2025-08-02 00:27:17,627 INFO Connection check task end

2025-08-02 00:27:18,757 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:20,631 INFO Connection check task start

2025-08-02 00:27:20,631 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:20,631 INFO Out dated connection ,size=0

2025-08-02 00:27:20,631 INFO Connection check task end

2025-08-02 00:27:21,761 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:23,633 INFO Connection check task start

2025-08-02 00:27:23,634 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:23,634 INFO Out dated connection ,size=0

2025-08-02 00:27:23,634 INFO Connection check task end

2025-08-02 00:27:24,762 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:26,637 INFO Connection check task start

2025-08-02 00:27:26,637 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:26,637 INFO Out dated connection ,size=0

2025-08-02 00:27:26,637 INFO Connection check task end

2025-08-02 00:27:27,766 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:29,642 INFO Connection check task start

2025-08-02 00:27:29,643 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:29,643 INFO Out dated connection ,size=0

2025-08-02 00:27:29,643 INFO Connection check task end

2025-08-02 00:27:30,767 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:32,648 INFO Connection check task start

2025-08-02 00:27:32,648 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:32,648 INFO Out dated connection ,size=0

2025-08-02 00:27:32,649 INFO Connection check task end

2025-08-02 00:27:33,771 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:35,653 INFO Connection check task start

2025-08-02 00:27:35,653 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:35,653 INFO Out dated connection ,size=0

2025-08-02 00:27:35,653 INFO Connection check task end

2025-08-02 00:27:36,777 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:38,657 INFO Connection check task start

2025-08-02 00:27:38,657 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:38,657 INFO Out dated connection ,size=0

2025-08-02 00:27:38,657 INFO Connection check task end

2025-08-02 00:27:39,779 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:41,659 INFO Connection check task start

2025-08-02 00:27:41,659 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:41,659 INFO Out dated connection ,size=0

2025-08-02 00:27:41,659 INFO Connection check task end

2025-08-02 00:27:42,782 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:44,665 INFO Connection check task start

2025-08-02 00:27:44,665 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:44,665 INFO Out dated connection ,size=0

2025-08-02 00:27:44,665 INFO Connection check task end

2025-08-02 00:27:45,787 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:47,666 INFO Connection check task start

2025-08-02 00:27:47,667 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:47,667 INFO Out dated connection ,size=0

2025-08-02 00:27:47,667 INFO Connection check task end

2025-08-02 00:27:48,792 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:50,672 INFO Connection check task start

2025-08-02 00:27:50,672 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:50,673 INFO Out dated connection ,size=0

2025-08-02 00:27:50,673 INFO Connection check task end

2025-08-02 00:27:51,796 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:53,675 INFO Connection check task start

2025-08-02 00:27:53,675 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:53,675 INFO Out dated connection ,size=0

2025-08-02 00:27:53,675 INFO Connection check task end

2025-08-02 00:27:54,798 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:56,680 INFO Connection check task start

2025-08-02 00:27:56,681 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:56,681 INFO Out dated connection ,size=0

2025-08-02 00:27:56,681 INFO Connection check task end

2025-08-02 00:27:57,800 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:27:59,682 INFO Connection check task start

2025-08-02 00:27:59,683 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:27:59,683 INFO Out dated connection ,size=0

2025-08-02 00:27:59,683 INFO Connection check task end

2025-08-02 00:28:00,800 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:02,688 INFO Connection check task start

2025-08-02 00:28:02,688 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:02,689 INFO Out dated connection ,size=0

2025-08-02 00:28:02,689 INFO Connection check task end

2025-08-02 00:28:03,806 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:05,693 INFO Connection check task start

2025-08-02 00:28:05,693 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:05,693 INFO Out dated connection ,size=0

2025-08-02 00:28:05,693 INFO Connection check task end

2025-08-02 00:28:06,807 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:08,697 INFO Connection check task start

2025-08-02 00:28:08,697 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:08,697 INFO Out dated connection ,size=0

2025-08-02 00:28:08,697 INFO Connection check task end

2025-08-02 00:28:09,808 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:11,700 INFO Connection check task start

2025-08-02 00:28:11,700 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:11,700 INFO Out dated connection ,size=0

2025-08-02 00:28:11,700 INFO Connection check task end

2025-08-02 00:28:12,814 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:14,701 INFO Connection check task start

2025-08-02 00:28:14,701 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:14,701 INFO Out dated connection ,size=0

2025-08-02 00:28:14,701 INFO Connection check task end

2025-08-02 00:28:15,817 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:17,702 INFO Connection check task start

2025-08-02 00:28:17,703 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:17,703 INFO Out dated connection ,size=0

2025-08-02 00:28:17,703 INFO Connection check task end

2025-08-02 00:28:18,615 INFO [1754065517844_127.0.0.1_53417]Connection unregistered successfully. 

2025-08-02 00:28:18,822 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:28:20,708 INFO Connection check task start

2025-08-02 00:28:20,708 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:28:20,709 INFO Out dated connection ,size=0

2025-08-02 00:28:20,709 INFO Connection check task end

2025-08-02 00:28:21,827 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:28:21,841 INFO [1754065370344_127.0.0.1_52608]Connection unregistered successfully. 

2025-08-02 00:28:23,711 INFO Connection check task start

2025-08-02 00:28:23,711 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:28:23,711 INFO Out dated connection ,size=0

2025-08-02 00:28:23,711 INFO Connection check task end

2025-08-02 00:28:24,829 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:28:26,714 INFO Connection check task start

2025-08-02 00:28:26,714 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:28:26,714 INFO Out dated connection ,size=0

2025-08-02 00:28:26,714 INFO Connection check task end

2025-08-02 00:28:27,834 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:28:29,716 INFO Connection check task start

2025-08-02 00:28:29,716 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:28:29,716 INFO Out dated connection ,size=0

2025-08-02 00:28:29,717 INFO Connection check task end

2025-08-02 00:28:30,838 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:28:32,720 INFO Connection check task start

2025-08-02 00:28:32,720 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:28:32,720 INFO Out dated connection ,size=0

2025-08-02 00:28:32,720 INFO Connection check task end

2025-08-02 00:28:33,842 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:28:35,721 INFO Connection check task start

2025-08-02 00:28:35,722 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-08-02 00:28:35,722 INFO Out dated connection ,size=0

2025-08-02 00:28:35,722 INFO Connection check task end

2025-08-02 00:28:36,844 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:28:37,032 INFO new connection registered successfully, connectionId = 1754065717007_127.0.0.1_54379,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54379, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065717007_127.0.0.1_54379', createTime=Sat Aug 02 00:28:37 CST 2025, lastActiveTime=1754065717032, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:28:38,726 INFO Connection check task start

2025-08-02 00:28:38,727 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:28:38,727 INFO Out dated connection ,size=0

2025-08-02 00:28:38,727 INFO Connection check task end

2025-08-02 00:28:39,846 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:28:41,732 INFO Connection check task start

2025-08-02 00:28:41,732 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-08-02 00:28:41,732 INFO Out dated connection ,size=0

2025-08-02 00:28:41,732 INFO Connection check task end

2025-08-02 00:28:42,850 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-08-02 00:28:43,184 INFO new connection registered successfully, connectionId = 1754065723160_127.0.0.1_54390,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54390, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065723160_127.0.0.1_54390', createTime=Sat Aug 02 00:28:43 CST 2025, lastActiveTime=1754065723184, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:28:44,734 INFO Connection check task start

2025-08-02 00:28:44,734 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:44,734 INFO Out dated connection ,size=0

2025-08-02 00:28:44,734 INFO Connection check task end

2025-08-02 00:28:45,851 INFO ConnectionMetrics, totalCount = 2, detail = {long_connection=2, long_polling=0}

2025-08-02 00:28:47,736 INFO Connection check task start

2025-08-02 00:28:47,736 INFO Long connection metrics detail ,Total count =2, sdkCount=2,clusterCount=0

2025-08-02 00:28:47,736 INFO Out dated connection ,size=0

2025-08-02 00:28:47,736 INFO Connection check task end

2025-08-02 00:28:48,746 INFO new connection registered successfully, connectionId = 1754065728719_127.0.0.1_54414,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54414, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065728719_127.0.0.1_54414', createTime=Sat Aug 02 00:28:48 CST 2025, lastActiveTime=1754065728746, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:28:48,856 INFO ConnectionMetrics, totalCount = 3, detail = {long_connection=3, long_polling=0}

2025-08-02 00:28:50,739 INFO Connection check task start

2025-08-02 00:28:50,739 INFO Long connection metrics detail ,Total count =3, sdkCount=3,clusterCount=0

2025-08-02 00:28:50,740 INFO Out dated connection ,size=0

2025-08-02 00:28:50,740 INFO Connection check task end

2025-08-02 00:28:51,859 INFO ConnectionMetrics, totalCount = 3, detail = {long_connection=3, long_polling=0}

2025-08-02 00:28:52,344 INFO new connection registered successfully, connectionId = 1754065732317_127.0.0.1_54420,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54420, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065732317_127.0.0.1_54420', createTime=Sat Aug 02 00:28:52 CST 2025, lastActiveTime=1754065732344, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:28:53,743 INFO Connection check task start

2025-08-02 00:28:53,743 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-08-02 00:28:53,743 INFO Out dated connection ,size=0

2025-08-02 00:28:53,743 INFO Connection check task end

2025-08-02 00:28:54,860 INFO ConnectionMetrics, totalCount = 4, detail = {long_connection=4, long_polling=0}

2025-08-02 00:28:56,747 INFO Connection check task start

2025-08-02 00:28:56,748 INFO Long connection metrics detail ,Total count =4, sdkCount=4,clusterCount=0

2025-08-02 00:28:56,748 INFO Out dated connection ,size=0

2025-08-02 00:28:56,748 INFO Connection check task end

2025-08-02 00:28:57,501 INFO new connection registered successfully, connectionId = 1754065737474_127.0.0.1_54435,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54435, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065737474_127.0.0.1_54435', createTime=Sat Aug 02 00:28:57 CST 2025, lastActiveTime=1754065737501, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:28:57,862 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:28:59,749 INFO Connection check task start

2025-08-02 00:28:59,750 INFO Long connection metrics detail ,Total count =5, sdkCount=5,clusterCount=0

2025-08-02 00:28:59,750 INFO Out dated connection ,size=0

2025-08-02 00:28:59,750 INFO Connection check task end

2025-08-02 00:29:00,863 INFO ConnectionMetrics, totalCount = 5, detail = {long_connection=5, long_polling=0}

2025-08-02 00:29:01,993 INFO new connection registered successfully, connectionId = 1754065741963_127.0.0.1_54444,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=54444, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1754065741963_127.0.0.1_54444', createTime=Sat Aug 02 00:29:01 CST 2025, lastActiveTime=1754065741993, appName='-', tenant='null', labels={module=naming, source=sdk}}} 

2025-08-02 00:29:02,754 INFO Connection check task start

2025-08-02 00:29:02,754 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:02,754 INFO Out dated connection ,size=0

2025-08-02 00:29:02,754 INFO Connection check task end

2025-08-02 00:29:03,867 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:05,759 INFO Connection check task start

2025-08-02 00:29:05,759 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:05,759 INFO Out dated connection ,size=0

2025-08-02 00:29:05,759 INFO Connection check task end

2025-08-02 00:29:06,872 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:08,761 INFO Connection check task start

2025-08-02 00:29:08,762 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:08,762 INFO Out dated connection ,size=0

2025-08-02 00:29:08,762 INFO Connection check task end

2025-08-02 00:29:09,875 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:11,767 INFO Connection check task start

2025-08-02 00:29:11,767 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:11,767 INFO Out dated connection ,size=0

2025-08-02 00:29:11,767 INFO Connection check task end

2025-08-02 00:29:12,875 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:14,772 INFO Connection check task start

2025-08-02 00:29:14,772 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:14,772 INFO Out dated connection ,size=0

2025-08-02 00:29:14,772 INFO Connection check task end

2025-08-02 00:29:15,879 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:17,776 INFO Connection check task start

2025-08-02 00:29:17,776 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:17,776 INFO Out dated connection ,size=0

2025-08-02 00:29:17,776 INFO Connection check task end

2025-08-02 00:29:18,883 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:20,779 INFO Connection check task start

2025-08-02 00:29:20,780 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:20,780 INFO Out dated connection ,size=0

2025-08-02 00:29:20,780 INFO Connection check task end

2025-08-02 00:29:21,887 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:23,785 INFO Connection check task start

2025-08-02 00:29:23,785 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:23,785 INFO Out dated connection ,size=0

2025-08-02 00:29:23,785 INFO Connection check task end

2025-08-02 00:29:24,888 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:26,789 INFO Connection check task start

2025-08-02 00:29:26,790 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:26,790 INFO Out dated connection ,size=0

2025-08-02 00:29:26,790 INFO Connection check task end

2025-08-02 00:29:27,893 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:29,795 INFO Connection check task start

2025-08-02 00:29:29,795 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:29,795 INFO Out dated connection ,size=0

2025-08-02 00:29:29,795 INFO Connection check task end

2025-08-02 00:29:30,898 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:32,800 INFO Connection check task start

2025-08-02 00:29:32,800 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:32,800 INFO Out dated connection ,size=0

2025-08-02 00:29:32,800 INFO Connection check task end

2025-08-02 00:29:33,902 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:35,805 INFO Connection check task start

2025-08-02 00:29:35,805 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:35,805 INFO Out dated connection ,size=0

2025-08-02 00:29:35,805 INFO Connection check task end

2025-08-02 00:29:36,904 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

2025-08-02 00:29:37,040 INFO Starting Nacos v2.3.0 using Java 23.0.2 on JStardeMacBook-Pro.local with PID 93182 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/target/nacos-server.jar started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/bin)

2025-08-02 00:29:37,040 INFO The following 1 profile is active: "standalone"

2025-08-02 00:29:37,103 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-08-02 00:29:37,103 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-08-02 00:29:37,104 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-08-02 00:29:37,179 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-08-02 00:29:37,225 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-08-02 00:29:37,230 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,235 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,236 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,237 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-08-02 00:29:37,354 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-08-02 00:29:38,159 INFO Tomcat initialized with port(s): 8848 (http)

2025-08-02 00:29:38,257 INFO Starting service [Tomcat]

2025-08-02 00:29:38,257 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-08-02 00:29:38,299 INFO Initializing Spring embedded WebApplicationContext

2025-08-02 00:29:38,300 INFO Root WebApplicationContext: initialization completed in 1230 ms

2025-08-02 00:29:38,405 INFO Nacos-related cluster resource initialization

2025-08-02 00:29:38,408 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-08-02 00:29:38,408 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-08-02 00:29:38,409 INFO The cluster resource is initialized

2025-08-02 00:29:38,530 INFO HikariPool-1 - Starting...

2025-08-02 00:29:38,611 INFO HikariPool-1 - Start completed.

2025-08-02 00:29:38,806 INFO Connection check task start

2025-08-02 00:29:38,806 INFO Long connection metrics detail ,Total count =6, sdkCount=6,clusterCount=0

2025-08-02 00:29:38,806 INFO Out dated connection ,size=0

2025-08-02 00:29:38,806 INFO Connection check task end

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-08-02 00:29:38,858 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-08-02 00:29:38,859 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-08-02 00:29:38,869 INFO Not configure type of control plugin, no limit control for current node.

2025-08-02 00:29:38,870 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@10643593, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@eca6a74]

2025-08-02 00:29:38,870 INFO No connection rule content found ,use default empty rule 

2025-08-02 00:29:38,874 INFO Fail to find connection runtime ejector for name nacos,use default

2025-08-02 00:29:38,877 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-08-02 00:29:38,877 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-08-02 00:29:38,877 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-08-02 00:29:38,877 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-08-02 00:29:38,877 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-08-02 00:29:38,877 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-08-02 00:29:38,918 INFO [MapperManager] findMapper dataSource: mysql, tableName: config_info

2025-08-02 00:29:38,923 INFO [MapperManager] findMapper dataSource: mysql, tableName: config_info_aggr

2025-08-02 00:29:38,936 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-08-02 00:29:39,025 INFO Ready to get current node abilities...

2025-08-02 00:29:39,026 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT, CLUSTER_CLIENT, SERVER]

2025-08-02 00:29:39,026 INFO Initialize current abilities finish...

2025-08-02 00:29:39,026 INFO Ready to get current node abilities...

2025-08-02 00:29:39,026 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-08-02 00:29:39,026 INFO Initialize current abilities finish...

2025-08-02 00:29:39,026 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-08-02 00:29:39,170 WARN Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'grpcSdkServer': Invocation of init method failed; nested exception is java.io.IOException: Failed to bind to address 0.0.0.0/0.0.0.0:9848

2025-08-02 00:29:39,173 INFO Stopping service [Tomcat]

2025-08-02 00:29:39,174 WARN The web application [nacos] appears to have started a thread named [nacos.publisher-com.alibaba.nacos.common.event.ServerConfigChangeEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
 java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
 java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
 java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
 com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(DefaultPublisher.java:111)
 com.alibaba.nacos.common.notify.DefaultPublisher.run(DefaultPublisher.java:95)

2025-08-02 00:29:39,174 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client.0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:343)
 org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221)
 org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.2] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.5] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.6] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.7] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.8] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.9] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.10] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.11] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.12] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.13] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.14] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos-http-async-client#I/O Reactor.15] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/sun.nio.ch.KQueue.poll(Native Method)
 java.base/sun.nio.ch.KQueueSelectorImpl.doSelect(KQueueSelectorImpl.java:125)
 java.base/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
 java.base/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
 org.apache.http.impl.nio.reactor.AbstractIOReactor.execute(AbstractIOReactor.java:255)
 org.apache.http.impl.nio.reactor.BaseIOReactor.execute(BaseIOReactor.java:104)
 org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor$Worker.run(AbstractMultiworkerIOReactor.java:591)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos.publisher-com.alibaba.nacos.core.cluster.MembersChangeEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/java.lang.Thread.sleepNanos0(Native Method)
 java.base/java.lang.Thread.sleepNanos(Thread.java:496)
 java.base/java.lang.Thread.sleep(Thread.java:527)
 com.alibaba.nacos.common.utils.ThreadUtils.sleep(ThreadUtils.java:45)
 com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(DefaultPublisher.java:106)
 com.alibaba.nacos.common.notify.DefaultPublisher.run(DefaultPublisher.java:95)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [nacos.publisher-com.alibaba.nacos.naming.consistency.ValueChangeEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/java.lang.Thread.sleepNanos0(Native Method)
 java.base/java.lang.Thread.sleepNanos(Thread.java:496)
 java.base/java.lang.Thread.sleep(Thread.java:527)
 com.alibaba.nacos.common.utils.ThreadUtils.sleep(ThreadUtils.java:45)
 com.alibaba.nacos.common.notify.DefaultPublisher.openEventHandler(DefaultPublisher.java:106)
 com.alibaba.nacos.common.notify.DefaultPublisher.run(DefaultPublisher.java:95)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [com.alibaba.nacos.naming.timer.0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [com.alibaba.nacos.naming.timer.1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
 java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
 java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [naming.publisher-ClientOperationEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/java.lang.Thread.sleepNanos0(Native Method)
 java.base/java.lang.Thread.sleepNanos(Thread.java:496)
 java.base/java.lang.Thread.sleep(Thread.java:527)
 com.alibaba.nacos.common.utils.ThreadUtils.sleep(ThreadUtils.java:45)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.waitSubscriberForInit(NamingEventPublisher.java:150)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.run(NamingEventPublisher.java:135)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [naming.publisher-MetadataEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
 java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
 java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
 java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.handleEvents(NamingEventPublisher.java:157)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.run(NamingEventPublisher.java:136)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [naming.publisher-ClientEvent] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
 java.base/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:4023)
 java.base/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3969)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712)
 java.base/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.handleEvents(NamingEventPublisher.java:157)
 com.alibaba.nacos.naming.core.v2.event.publisher.NamingEventPublisher.run(NamingEventPublisher.java:136)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [com.alibaba.nacos.naming.remote-connection-manager.0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [HikariPool-1 housekeeper] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,175 WARN The web application [nacos] appears to have started a thread named [MySQL Statement Cancellation Timer] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/java.lang.Object.wait0(Native Method)
 java.base/java.lang.Object.wait(Object.java:378)
 java.base/java.util.TimerThread.mainLoop(Timer.java:569)
 java.base/java.util.TimerThread.run(Timer.java:522)

2025-08-02 00:29:39,176 WARN The web application [nacos] appears to have started a thread named [com.alibaba.nacos.persistence.timer.0] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,176 WARN The web application [nacos] appears to have started a thread named [HikariPool-1 connection adder] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:460)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1575)

2025-08-02 00:29:39,177 INFO Nacos Log files: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/logs

2025-08-02 00:29:39,177 INFO Nacos Log files: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/conf

2025-08-02 00:29:39,177 INFO Nacos Log files: /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/data

2025-08-02 00:29:39,177 ERROR Startup errors : 

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'grpcSdkServer': Invocation of init method failed; nested exception is java.io.IOException: Failed to bind to address 0.0.0.0/0.0.0.0:9848
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.alibaba.nacos.Nacos.main(Nacos.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.PropertiesLauncher.main(PropertiesLauncher.java:467)
Caused by: java.io.IOException: Failed to bind to address 0.0.0.0/0.0.0.0:9848
	at io.grpc.netty.shaded.io.grpc.netty.NettyServer.start(NettyServer.java:328)
	at io.grpc.internal.ServerImpl.start(ServerImpl.java:185)
	at io.grpc.internal.ServerImpl.start(ServerImpl.java:94)
	at com.alibaba.nacos.core.remote.grpc.BaseGrpcServer.startServer(BaseGrpcServer.java:95)
	at com.alibaba.nacos.core.remote.BaseRpcServer.start(BaseRpcServer.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	... 24 common frames omitted
Caused by: java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:565)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at io.grpc.netty.shaded.io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260)
	at io.grpc.netty.shaded.io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:356)
	at io.grpc.netty.shaded.io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.grpc.netty.shaded.io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-02 00:29:39,178 WARN [WatchFileCenter] start close

2025-08-02 00:29:39,178 WARN [WatchFileCenter] start to shutdown this watcher which is watch : /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/conf

2025-08-02 00:29:39,178 WARN [WatchFileCenter] already closed

2025-08-02 00:29:39,178 WARN [NotifyCenter] Start destroying Publisher

2025-08-02 00:29:39,178 WARN [NotifyCenter] Destruction of the end

2025-08-02 00:29:39,178 ERROR Nacos failed to start, please see /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/infrastructure/nacos/nacos/logs/nacos.log for more details.

2025-08-02 00:29:39,183 INFO 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.

2025-08-02 00:29:39,193 ERROR Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'grpcSdkServer': Invocation of init method failed; nested exception is java.io.IOException: Failed to bind to address 0.0.0.0/0.0.0.0:9848
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.alibaba.nacos.Nacos.main(Nacos.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.PropertiesLauncher.main(PropertiesLauncher.java:467)
Caused by: java.io.IOException: Failed to bind to address 0.0.0.0/0.0.0.0:9848
	at io.grpc.netty.shaded.io.grpc.netty.NettyServer.start(NettyServer.java:328)
	at io.grpc.internal.ServerImpl.start(ServerImpl.java:185)
	at io.grpc.internal.ServerImpl.start(ServerImpl.java:94)
	at com.alibaba.nacos.core.remote.grpc.BaseGrpcServer.startServer(BaseGrpcServer.java:95)
	at com.alibaba.nacos.core.remote.BaseRpcServer.start(BaseRpcServer.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
	... 24 common frames omitted
Caused by: java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:565)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.netBind(ServerSocketChannelImpl.java:344)
	at java.base/sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:301)
	at io.grpc.netty.shaded.io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260)
	at io.grpc.netty.shaded.io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:356)
	at io.grpc.netty.shaded.io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)
	at io.grpc.netty.shaded.io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-02 00:29:39,194 WARN [HttpClientBeanHolder] Start destroying common HttpClient

2025-08-02 00:29:39,194 WARN [ThreadPoolManager] Start destroying ThreadPool

2025-08-02 00:29:39,194 WARN [ThreadPoolManager] Destruction of the end

2025-08-02 00:29:39,908 INFO ConnectionMetrics, totalCount = 6, detail = {long_connection=6, long_polling=0}

