2025-08-01 15:20:24,975 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}

2025-08-01 15:20:25,499 INFO [PUSH-SUCC] 6ms, all delay time 552ms, SLA 552ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 15:45:55,787 INFO [PUSH-SUCC] 2ms, all delay time 551ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 15:53:29,748 INFO [PUSH-SUCC] 3ms, all delay time 551ms, SLA 551ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 15:53:48,069 INFO [PUSH-SUCC] 4ms, all delay time 547ms, SLA 547ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 16:06:25,983 INFO [PUSH-SUCC] 2ms, all delay time 594ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 16:10:43,789 INFO [PUSH-SUCC] 3ms, all delay time 582ms, SLA 582ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 16:11:32,284 INFO [PUSH-SUCC] 4ms, all delay time 600ms, SLA 600ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 16:12:15,417 INFO [PUSH-SUCC] 5ms, all delay time 544ms, SLA 544ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 16:12:55,785 INFO [PUSH-SUCC] 2ms, all delay time 603ms, SLA 603ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 16:13:32,096 INFO [PUSH-SUCC] 3ms, all delay time 601ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 16:31:56,182 INFO [PUSH-SUCC] 3ms, all delay time 578ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 16:33:13,800 INFO [PUSH-SUCC] 3ms, all delay time 505ms, SLA 505ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 16:33:22,827 INFO [PUSH-SUCC] 3ms, all delay time 538ms, SLA 538ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 16:37:40,186 INFO [PUSH-SUCC] 3ms, all delay time 592ms, SLA 592ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 16:37:51,945 INFO [PUSH-SUCC] 3ms, all delay time 518ms, SLA 518ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 16:50:17,515 INFO [PUSH-SUCC] 1ms, all delay time 529ms, SLA 529ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 16:50:28,783 INFO [PUSH-SUCC] 3ms, all delay time 526ms, SLA 526ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:28:56,586 INFO [PUSH-SUCC] 1ms, all delay time 597ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0

2025-08-01 17:29:14,511 INFO [PUSH-SUCC] 3ms, all delay time 583ms, SLA 583ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:30:04,925 INFO [PUSH-SUCC] 4ms, all delay time 557ms, SLA 557ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 17:31:04,648 INFO [PUSH-SUCC] 3ms, all delay time 573ms, SLA 573ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:32:00,487 INFO [PUSH-SUCC] 5ms, all delay time 548ms, SLA 548ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 17:32:14,915 INFO [PUSH-SUCC] 6ms, all delay time 519ms, SLA 519ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:32:51,696 INFO [PUSH-SUCC] 3ms, all delay time 590ms, SLA 590ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 17:33:01,351 INFO [PUSH-SUCC] 3ms, all delay time 605ms, SLA 605ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:33:32,354 INFO [PUSH-SUCC] 2ms, all delay time 603ms, SLA 603ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 17:33:42,514 INFO [PUSH-SUCC] 4ms, all delay time 576ms, SLA 576ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 17:34:18,646 INFO [PUSH-SUCC] 3ms, all delay time 605ms, SLA 605ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 17:36:17,500 INFO [PUSH-SUCC] 7ms, all delay time 580ms, SLA 580ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:27:35,997 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}

2025-08-01 18:27:36,552 INFO [PUSH-SUCC] 18ms, all delay time 555ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 18:27:36,553 INFO [PUSH-SUCC] 19ms, all delay time 556ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 18:27:36,555 INFO [PUSH-SUCC] 21ms, all delay time 590ms, SLA 590ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:27:36,556 INFO [PUSH-SUCC] 22ms, all delay time 560ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 18:27:36,557 INFO [PUSH-SUCC] 23ms, all delay time 561ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:27:36,559 INFO [PUSH-SUCC] 24ms, all delay time 563ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:31:26,592 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}

2025-08-01 18:31:27,088 INFO [PUSH-SUCC] 6ms, all delay time 528ms, SLA 528ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:31:27,189 INFO [PUSH-SUCC] 5ms, all delay time 597ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:31:27,191 INFO [PUSH-SUCC] 6ms, all delay time 599ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:31:27,192 INFO [PUSH-SUCC] 7ms, all delay time 600ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 18:31:27,194 INFO [PUSH-SUCC] 9ms, all delay time 602ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 18:31:27,196 INFO [PUSH-SUCC] 12ms, all delay time 603ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 18:34:26,700 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}

2025-08-01 18:34:27,250 INFO [PUSH-SUCC] 10ms, all delay time 550ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 18:34:27,251 INFO [PUSH-SUCC] 11ms, all delay time 551ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:34:27,252 INFO [PUSH-SUCC] 11ms, all delay time 552ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:34:27,254 INFO [PUSH-SUCC] 13ms, all delay time 554ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 18:34:27,257 INFO [PUSH-SUCC] 16ms, all delay time 593ms, SLA 593ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:34:32,243 ERROR [PUSH-FAIL] 5003ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, reason=Timeout After 5000 milliseconds, requestId=42, connectionId=1754044466511_127.0.0.1_50616, target=192.168.0.110

2025-08-01 18:34:32,244 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=42, connectionId=1754044466511_127.0.0.1_50616
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 18:34:33,341 INFO [PUSH-SUCC] 4ms, all delay time 1096ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 18:35:54,292 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}

2025-08-01 18:35:54,826 INFO [PUSH-SUCC] 7ms, all delay time 534ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 18:35:54,828 INFO [PUSH-SUCC] 9ms, all delay time 536ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 18:35:54,829 INFO [PUSH-SUCC] 10ms, all delay time 571ms, SLA 572ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:35:54,830 INFO [PUSH-SUCC] 11ms, all delay time 538ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 18:35:54,831 INFO [PUSH-SUCC] 12ms, all delay time 539ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:35:59,820 ERROR [PUSH-FAIL] 5001ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, reason=Timeout After 5000 milliseconds, requestId=50, connectionId=1754044554103_127.0.0.1_51169, target=192.168.0.110

2025-08-01 18:35:59,820 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=50, connectionId=1754044554103_127.0.0.1_51169
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 18:36:00,895 INFO [PUSH-SUCC] 3ms, all delay time 1074ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 18:45:03,139 INFO [PUSH-SUCC] 4ms, all delay time 588ms, SLA 589ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:45:03,143 INFO [PUSH-SUCC] 8ms, all delay time 592ms, SLA 593ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:45:07,997 INFO [PUSH-SUCC] 3ms, all delay time 524ms, SLA 525ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:45:13,153 INFO [PUSH-SUCC] 5ms, all delay time 534ms, SLA 534ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:45:17,903 INFO [PUSH-SUCC] 2ms, all delay time 598ms, SLA 598ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:45:22,553 INFO [PUSH-SUCC] 4ms, all delay time 545ms, SLA 545ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:46:03,008 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}

2025-08-01 18:46:03,584 INFO [PUSH-SUCC] 9ms, all delay time 576ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0

2025-08-01 18:46:03,587 INFO [PUSH-SUCC] 11ms, all delay time 579ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0

2025-08-01 18:46:03,593 INFO [PUSH-SUCC] 17ms, all delay time 622ms, SLA 622ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:46:03,595 INFO [PUSH-SUCC] 20ms, all delay time 587ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0

2025-08-01 18:46:03,595 INFO [PUSH-SUCC] 20ms, all delay time 587ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0

2025-08-01 18:46:03,597 INFO [PUSH-SUCC] 20ms, all delay time 589ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0

2025-08-01 18:46:08,556 INFO [PUSH-SUCC] 7ms, all delay time 606ms, SLA 606ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:46:12,660 INFO [PUSH-SUCC] 5ms, all delay time 561ms, SLA 561ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:46:19,268 INFO [PUSH-SUCC] 7ms, all delay time 547ms, SLA 547ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:46:23,816 INFO [PUSH-SUCC] 7ms, all delay time 544ms, SLA 544ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:46:30,522 INFO [PUSH-SUCC] 4ms, all delay time 579ms, SLA 579ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:52:39,619 INFO [PUSH-SUCC] 3ms, all delay time 562ms, SLA 563ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:57:30,564 INFO [PUSH-SUCC] 6ms, all delay time 568ms, SLA 568ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:58:29,427 INFO [PUSH-SUCC] 2ms, all delay time 539ms, SLA 539ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 18:58:50,389 INFO [PUSH-SUCC] 3ms, all delay time 585ms, SLA 585ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 18:59:47,481 INFO [PUSH-SUCC] 5ms, all delay time 603ms, SLA 603ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 19:47:57,207 INFO [PUSH-SUCC] 8ms, all delay time 588ms, SLA 588ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 19:56:40,340 INFO [PUSH-SUCC] 3ms, all delay time 553ms, SLA 553ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 19:56:47,475 INFO [PUSH-SUCC] 8ms, all delay time 525ms, SLA 525ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 20:09:09,986 INFO [PUSH-SUCC] 4ms, all delay time 594ms, SLA 594ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 20:09:19,542 INFO [PUSH-SUCC] 4ms, all delay time 599ms, SLA 599ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 20:11:33,909 INFO [PUSH-SUCC] 3ms, all delay time 577ms, SLA 577ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 20:11:41,723 INFO [PUSH-SUCC] 2ms, all delay time 547ms, SLA 548ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 20:26:33,602 INFO [PUSH-SUCC] 7ms, all delay time 509ms, SLA 509ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 20:26:44,550 INFO [PUSH-SUCC] 3ms, all delay time 575ms, SLA 575ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 20:35:36,556 INFO [PUSH-SUCC] 6ms, all delay time 509ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:06:48,283 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}

2025-08-01 21:06:48,844 INFO [PUSH-SUCC] 7ms, all delay time 561ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:06:48,848 INFO [PUSH-SUCC] 11ms, all delay time 565ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:06:48,849 INFO [PUSH-SUCC] 12ms, all delay time 565ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:06:48,852 INFO [PUSH-SUCC] 15ms, all delay time 602ms, SLA 602ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:06:48,854 INFO [PUSH-SUCC] 17ms, all delay time 571ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:06:48,854 INFO [PUSH-SUCC] 17ms, all delay time 571ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:07:59,950 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}

2025-08-01 21:08:00,471 INFO [PUSH-SUCC] 6ms, all delay time 521ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:08:00,475 INFO [PUSH-SUCC] 10ms, all delay time 558ms, SLA 558ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:08:00,477 INFO [PUSH-SUCC] 12ms, all delay time 527ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:08:00,479 INFO [PUSH-SUCC] 14ms, all delay time 529ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:08:00,480 INFO [PUSH-SUCC] 15ms, all delay time 530ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:08:00,482 INFO [PUSH-SUCC] 17ms, all delay time 532ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:14:28,046 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}

2025-08-01 21:14:28,623 INFO [PUSH-SUCC] 14ms, all delay time 577ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:14:28,626 INFO [PUSH-SUCC] 17ms, all delay time 580ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:14:28,628 INFO [PUSH-SUCC] 18ms, all delay time 582ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:14:28,631 INFO [PUSH-SUCC] 21ms, all delay time 613ms, SLA 614ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:14:28,633 INFO [PUSH-SUCC] 23ms, all delay time 587ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:14:33,614 ERROR [PUSH-FAIL] 5005ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, reason=Timeout After 5000 milliseconds, requestId=101, connectionId=1754054067878_127.0.0.1_54668, target=192.168.0.110

2025-08-01 21:14:33,614 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=101, connectionId=1754054067878_127.0.0.1_54668
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 21:14:34,635 INFO [PUSH-SUCC] 11ms, all delay time 1021ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:19:16,607 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=17}

2025-08-01 21:19:17,171 INFO [PUSH-SUCC] 10ms, all delay time 564ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:19:17,173 INFO [PUSH-SUCC] 12ms, all delay time 594ms, SLA 594ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:19:17,175 INFO [PUSH-SUCC] 14ms, all delay time 568ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:19:17,176 INFO [PUSH-SUCC] 16ms, all delay time 569ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:19:17,177 INFO [PUSH-SUCC] 16ms, all delay time 570ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:19:22,161 ERROR [PUSH-FAIL] 5001ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, reason=Timeout After 5000 milliseconds, requestId=107, connectionId=1754054356438_127.0.0.1_56302, target=192.168.0.110

2025-08-01 21:19:22,161 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=107, connectionId=1754054356438_127.0.0.1_56302
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 21:19:23,257 INFO [PUSH-SUCC] 4ms, all delay time 1096ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:22:31,727 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}

2025-08-01 21:22:32,216 INFO [PUSH-SUCC] 6ms, all delay time 519ms, SLA 519ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=19}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:22:32,315 INFO [PUSH-SUCC] 1ms, all delay time 588ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:22:32,316 INFO [PUSH-SUCC] 2ms, all delay time 589ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:22:32,317 INFO [PUSH-SUCC] 4ms, all delay time 590ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:22:32,318 INFO [PUSH-SUCC] 5ms, all delay time 591ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:22:32,319 INFO [PUSH-SUCC] 5ms, all delay time 592ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:25:52,602 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=21}

2025-08-01 21:25:53,137 INFO [PUSH-SUCC] 6ms, all delay time 535ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:25:53,141 INFO [PUSH-SUCC] 10ms, all delay time 566ms, SLA 566ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=21}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:25:53,143 INFO [PUSH-SUCC] 12ms, all delay time 541ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:25:53,144 INFO [PUSH-SUCC] 13ms, all delay time 542ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:25:53,146 INFO [PUSH-SUCC] 15ms, all delay time 543ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:25:53,147 INFO [PUSH-SUCC] 16ms, all delay time 544ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:26:18,363 INFO [PUSH-SUCC] 5ms, all delay time 588ms, SLA 588ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:31:14,716 INFO [PUSH-SUCC] 12ms, all delay time 537ms, SLA 537ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:31:53,351 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}

2025-08-01 21:31:53,927 INFO [PUSH-SUCC] 15ms, all delay time 576ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:31:53,927 INFO [PUSH-SUCC] 15ms, all delay time 576ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:31:53,929 INFO [PUSH-SUCC] 16ms, all delay time 577ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:31:53,931 INFO [PUSH-SUCC] 19ms, all delay time 609ms, SLA 609ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:31:53,933 INFO [PUSH-SUCC] 21ms, all delay time 581ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:31:53,936 INFO [PUSH-SUCC] 20ms, all delay time 584ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 21:35:52,870 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}

2025-08-01 21:35:53,437 INFO [PUSH-SUCC] 8ms, all delay time 567ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:35:53,438 INFO [PUSH-SUCC] 9ms, all delay time 568ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 21:35:53,440 INFO [PUSH-SUCC] 11ms, all delay time 570ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:35:53,442 INFO [PUSH-SUCC] 13ms, all delay time 601ms, SLA 601ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:35:53,444 INFO [PUSH-SUCC] 15ms, all delay time 574ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:35:58,433 ERROR [PUSH-FAIL] 5004ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, reason=Timeout After 5000 milliseconds, requestId=134, connectionId=1754055352705_127.0.0.1_61849, target=192.168.0.110

2025-08-01 21:35:58,433 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=134, connectionId=1754055352705_127.0.0.1_61849
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 21:35:59,532 INFO [PUSH-SUCC] 5ms, all delay time 1099ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:38:08,633 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}

2025-08-01 21:38:09,128 INFO [PUSH-SUCC] 12ms, all delay time 522ms, SLA 522ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:38:09,232 INFO [PUSH-SUCC] 11ms, all delay time 597ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:38:09,235 INFO [PUSH-SUCC] 13ms, all delay time 601ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:38:09,236 INFO [PUSH-SUCC] 14ms, all delay time 601ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:38:09,238 INFO [PUSH-SUCC] 15ms, all delay time 603ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 21:38:09,240 INFO [PUSH-SUCC] 18ms, all delay time 605ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:39:53,715 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}

2025-08-01 21:39:54,243 INFO [PUSH-SUCC] 12ms, all delay time 528ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 21:39:54,245 INFO [PUSH-SUCC] 14ms, all delay time 528ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:39:54,247 INFO [PUSH-SUCC] 16ms, all delay time 532ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:39:54,249 INFO [PUSH-SUCC] 18ms, all delay time 532ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1

2025-08-01 21:39:54,252 INFO [PUSH-SUCC] 21ms, all delay time 567ms, SLA 567ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:39:54,253 INFO [PUSH-SUCC] 21ms, all delay time 538ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1

2025-08-01 21:42:32,665 INFO [PUSH-SUCC] 9ms, all delay time 565ms, SLA 566ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:42:40,085 INFO [PUSH-SUCC] 3ms, all delay time 520ms, SLA 520ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:47:35,251 INFO [PUSH-SUCC] 9ms, all delay time 550ms, SLA 550ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:47:43,519 INFO [PUSH-SUCC] 6ms, all delay time 532ms, SLA 532ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:49:18,056 INFO [PUSH-SUCC] 12ms, all delay time 513ms, SLA 514ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:49:27,152 INFO [PUSH-SUCC] 7ms, all delay time 555ms, SLA 555ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:50:23,009 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}

2025-08-01 21:50:23,514 INFO [PUSH-SUCC] 8ms, all delay time 536ms, SLA 536ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:50:23,613 INFO [PUSH-SUCC] 2ms, all delay time 604ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:50:23,614 INFO [PUSH-SUCC] 3ms, all delay time 605ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:50:23,615 INFO [PUSH-SUCC] 4ms, all delay time 606ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 21:50:23,616 INFO [PUSH-SUCC] 5ms, all delay time 607ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 21:50:23,618 INFO [PUSH-SUCC] 7ms, all delay time 609ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 21:55:54,027 INFO [PUSH-SUCC] 3ms, all delay time 531ms, SLA 531ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:56:03,228 INFO [PUSH-SUCC] 3ms, all delay time 520ms, SLA 520ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 21:59:20,508 INFO [PUSH-SUCC] 6ms, all delay time 548ms, SLA 548ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 21:59:46,853 INFO [PUSH-SUCC] 4ms, all delay time 557ms, SLA 557ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:02:37,779 INFO [PUSH-SUCC] 5ms, all delay time 507ms, SLA 507ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:02:48,414 INFO [PUSH-SUCC] 2ms, all delay time 591ms, SLA 591ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:03:59,721 INFO [PUSH-SUCC] 7ms, all delay time 509ms, SLA 509ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=14}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:04:56,195 INFO [PUSH-SUCC] 3ms, all delay time 522ms, SLA 522ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:08:36,626 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}

2025-08-01 22:08:37,192 INFO [PUSH-SUCC] 6ms, all delay time 566ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 22:08:37,193 INFO [PUSH-SUCC] 7ms, all delay time 567ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:08:37,195 INFO [PUSH-SUCC] 9ms, all delay time 569ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1

2025-08-01 22:08:37,198 INFO [PUSH-SUCC] 12ms, all delay time 605ms, SLA 605ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:08:37,199 INFO [PUSH-SUCC] 13ms, all delay time 573ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:08:42,189 ERROR [PUSH-FAIL] 5003ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, reason=Timeout After 5000 milliseconds, requestId=175, connectionId=1754057316460_127.0.0.1_56565, target=192.168.0.110

2025-08-01 22:08:42,189 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=175, connectionId=1754057316460_127.0.0.1_56565
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 22:08:43,289 INFO [PUSH-SUCC] 5ms, all delay time 1099ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 22:11:20,613 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}

2025-08-01 22:11:21,181 INFO [PUSH-SUCC] 9ms, all delay time 568ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1

2025-08-01 22:11:21,184 INFO [PUSH-SUCC] 12ms, all delay time 599ms, SLA 599ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:11:21,186 INFO [PUSH-SUCC] 14ms, all delay time 573ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:11:21,187 INFO [PUSH-SUCC] 15ms, all delay time 574ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 22:11:21,189 INFO [PUSH-SUCC] 16ms, all delay time 576ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 22:11:26,177 ERROR [PUSH-FAIL] 5005ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, reason=Timeout After 5000 milliseconds, requestId=180, connectionId=1754057480448_127.0.0.1_57561, target=192.168.0.110

2025-08-01 22:11:26,178 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=180, connectionId=1754057480448_127.0.0.1_57561
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 22:11:27,273 INFO [PUSH-SUCC] 4ms, all delay time 1095ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:13:10,947 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}

2025-08-01 22:13:11,516 INFO [PUSH-SUCC] 13ms, all delay time 568ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 22:13:11,518 INFO [PUSH-SUCC] 15ms, all delay time 571ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1

2025-08-01 22:13:11,522 INFO [PUSH-SUCC] 20ms, all delay time 604ms, SLA 604ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:13:11,523 INFO [PUSH-SUCC] 21ms, all delay time 576ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:13:11,525 INFO [PUSH-SUCC] 23ms, all delay time 578ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:13:16,521 ERROR [PUSH-FAIL] 5019ms, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, reason=Timeout After 5000 milliseconds, requestId=188, connectionId=1754057590780_127.0.0.1_58201, target=192.168.0.110

2025-08-01 22:13:16,521 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=188, connectionId=1754057590780_127.0.0.1_58201
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 22:13:17,592 INFO [PUSH-SUCC] 3ms, all delay time 1071ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 22:15:10,822 INFO [PUSH] Task merge for Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=17}

2025-08-01 22:15:11,402 INFO [PUSH-SUCC] 7ms, all delay time 579ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='user-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1

2025-08-01 22:15:11,404 INFO [PUSH-SUCC] 9ms, all delay time 582ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1

2025-08-01 22:15:11,406 INFO [PUSH-SUCC] 11ms, all delay time 584ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=15}, originalSize=1, DataSize=1

2025-08-01 22:15:11,407 INFO [PUSH-SUCC] 13ms, all delay time 585ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:15:11,411 INFO [PUSH-SUCC] 15ms, all delay time 617ms, SLA 617ms, Service{namespace='public', group='DEFAULT_GROUP', name='gateway-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:15:16,396 ERROR [PUSH-FAIL] 5002ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, reason=Timeout After 5000 milliseconds, requestId=195, connectionId=1754057710652_127.0.0.1_58891, target=192.168.0.110

2025-08-01 22:15:16,396 ERROR Reason detail: 

java.util.concurrent.TimeoutException: Timeout After 5000 milliseconds, requestId=195, connectionId=1754057710652_127.0.0.1_58891
	at com.alibaba.nacos.api.remote.DefaultRequestFuture$TimeoutHandler.run(DefaultRequestFuture.java:194)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
2025-08-01 22:15:17,482 INFO [PUSH-SUCC] 5ms, all delay time 1085ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1

2025-08-01 22:19:46,433 INFO [PUSH-SUCC] 6ms, all delay time 560ms, SLA 560ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=4}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:19:56,024 INFO [PUSH-SUCC] 6ms, all delay time 519ms, SLA 519ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=5}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:21:43,141 INFO [PUSH-SUCC] 7ms, all delay time 551ms, SLA 551ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=6}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:21:52,453 INFO [PUSH-SUCC] 3ms, all delay time 541ms, SLA 541ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=7}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:34:15,243 INFO [PUSH-SUCC] 3ms, all delay time 533ms, SLA 533ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=16}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:34:23,707 INFO [PUSH-SUCC] 6ms, all delay time 562ms, SLA 562ms, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:42:19,907 INFO [PUSH-SUCC] 5ms, all delay time 510ms, SLA 511ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=8}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:42:28,257 INFO [PUSH-SUCC] 6ms, all delay time 554ms, SLA 554ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:43:28,559 INFO [PUSH-SUCC] 5ms, all delay time 546ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1

2025-08-01 22:43:59,933 INFO [PUSH-SUCC] 11ms, all delay time 559ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=9}, originalSize=1, DataSize=1

2025-08-01 22:46:59,351 INFO [PUSH-SUCC] 8ms, all delay time 592ms, SLA 593ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:46:59,353 INFO [PUSH-SUCC] 10ms, all delay time 593ms, SLA 594ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=10}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 22:47:26,302 INFO [PUSH-SUCC] 6ms, all delay time 571ms, SLA 571ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:47:26,302 INFO [PUSH-SUCC] 6ms, all delay time 571ms, SLA 571ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=11}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 22:48:24,916 INFO [PUSH-SUCC] 11ms, all delay time 539ms for subscriber 192.168.0.110, Service{namespace='public', group='DEFAULT_GROUP', name='learning-service', ephemeral=true, revision=17}, originalSize=1, DataSize=1

2025-08-01 23:33:07,554 INFO [PUSH-SUCC] 2ms, all delay time 537ms, SLA 537ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 23:33:07,554 INFO [PUSH-SUCC] 3ms, all delay time 537ms, SLA 537ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 23:33:15,385 INFO [PUSH-SUCC] 10ms, all delay time 526ms, SLA 526ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 23:33:15,385 INFO [PUSH-SUCC] 9ms, all delay time 526ms, SLA 526ms, Service{namespace='public', group='DEFAULT_GROUP', name='course-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 23:34:27,425 INFO [PUSH-SUCC] 2ms, all delay time 600ms, SLA 600ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 23:34:35,987 INFO [PUSH-SUCC] 2ms, all delay time 521ms, SLA 521ms, Service{namespace='public', group='DEFAULT_GROUP', name='recommendation-service', ephemeral=true, revision=13}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 23:39:20,584 INFO [PUSH-SUCC] 6ms, all delay time 528ms, SLA 528ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=12}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 23:44:14,888 INFO [PUSH-SUCC] 8ms, all delay time 534ms, SLA 534ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=1}, originalSize=1, DataSize=1, target=192.168.0.110

2025-08-01 23:45:23,398 INFO [PUSH-SUCC] 5ms, all delay time 558ms, SLA 558ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=2}, originalSize=0, DataSize=0, target=192.168.0.110

2025-08-01 23:45:29,792 INFO [PUSH-SUCC] 8ms, all delay time 590ms, SLA 590ms, Service{namespace='public', group='DEFAULT_GROUP', name='discussion-service', ephemeral=true, revision=3}, originalSize=1, DataSize=1, target=192.168.0.110

