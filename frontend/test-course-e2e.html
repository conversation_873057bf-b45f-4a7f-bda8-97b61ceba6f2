<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程功能端到端测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .flow-step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }
        .course-data { border: 1px solid #ddd; margin: 10px 0; padding: 10px; background: #f8f9fa; }
        pre { background: #f1f1f1; padding: 10px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>课程功能端到端测试</h1>
    <p>这个测试将模拟完整的课程功能使用流程</p>
    
    <div class="test-section">
        <h2>测试流程概览</h2>
        <div class="flow-step">1. 浏览课程列表</div>
        <div class="flow-step">2. 搜索特定课程</div>
        <div class="flow-step">3. 查看课程详情</div>
        <div class="flow-step">4. 报名课程</div>
        <div class="flow-step">5. 教师创建新课程</div>
        <div class="flow-step">6. 教师管理课程</div>
        <div class="flow-step">7. 编辑课程信息</div>
    </div>
    
    <div class="test-section">
        <h2>自动化测试</h2>
        <button onclick="runFullE2ETest()">运行完整端到端测试</button>
        <div id="e2eResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>手动测试页面</h2>
        <button onclick="openCoursesPage()">打开课程列表页面</button>
        <button onclick="openCourseDetail()">打开课程详情页面</button>
        <button onclick="openTeacherManage()">打开教师管理页面</button>
        <button onclick="openCourseCreate()">打开课程创建页面</button>
        <div id="manualResult" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082';
        const FRONTEND_BASE = 'http://localhost:8080';
        const STUDENT_ID = '4';
        const TEACHER_ID = '1';
        
        async function runFullE2ETest() {
            const resultDiv = document.getElementById('e2eResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在运行端到端测试...</div>';
            
            let testResults = [];
            
            try {
                // 步骤1: 浏览课程列表
                resultDiv.innerHTML += '<div class="info">📋 步骤1: 测试课程列表功能...</div>';
                const listResult = await testCourseList();
                testResults.push({step: '课程列表', success: listResult.success, message: listResult.message});
                
                // 步骤2: 搜索课程
                resultDiv.innerHTML += '<div class="info">🔍 步骤2: 测试课程搜索功能...</div>';
                const searchResult = await testCourseSearch();
                testResults.push({step: '课程搜索', success: searchResult.success, message: searchResult.message});
                
                // 步骤3: 查看课程详情
                resultDiv.innerHTML += '<div class="info">📖 步骤3: 测试课程详情功能...</div>';
                const detailResult = await testCourseDetail();
                testResults.push({step: '课程详情', success: detailResult.success, message: detailResult.message});
                
                // 步骤4: 课程报名
                resultDiv.innerHTML += '<div class="info">✍️ 步骤4: 测试课程报名功能...</div>';
                const enrollResult = await testCourseEnroll();
                testResults.push({step: '课程报名', success: enrollResult.success, message: enrollResult.message});
                
                // 步骤5: 教师创建课程
                resultDiv.innerHTML += '<div class="info">➕ 步骤5: 测试课程创建功能...</div>';
                const createResult = await testCourseCreate();
                testResults.push({step: '课程创建', success: createResult.success, message: createResult.message, courseId: createResult.courseId});
                
                // 步骤6: 教师管理课程
                resultDiv.innerHTML += '<div class="info">👨‍🏫 步骤6: 测试教师课程管理...</div>';
                const manageResult = await testTeacherManage();
                testResults.push({step: '教师管理', success: manageResult.success, message: manageResult.message});
                
                // 步骤7: 编辑课程
                if (createResult.courseId) {
                    resultDiv.innerHTML += '<div class="info">✏️ 步骤7: 测试课程编辑功能...</div>';
                    const editResult = await testCourseEdit(createResult.courseId);
                    testResults.push({step: '课程编辑', success: editResult.success, message: editResult.message});
                }
                
                // 生成测试报告
                generateTestReport(testResults);
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ 测试过程中发生错误: ${error.message}</div>`;
            }
        }
        
        async function testCourseList() {
            try {
                const response = await fetch(`${API_BASE}/api/course/list?page=1&size=10`);
                const data = await response.json();
                
                if (data.success && data.data.records && data.data.records.length > 0) {
                    return {success: true, message: `获取到${data.data.records.length}门课程`};
                } else {
                    return {success: false, message: '课程列表为空或API失败'};
                }
            } catch (error) {
                return {success: false, message: `API请求失败: ${error.message}`};
            }
        }
        
        async function testCourseSearch() {
            try {
                const response = await fetch(`${API_BASE}/api/course/list?keyword=Java`);
                const data = await response.json();
                
                if (data.success && data.data.records) {
                    return {success: true, message: `搜索"Java"找到${data.data.records.length}门课程`};
                } else {
                    return {success: false, message: '搜索功能失败'};
                }
            } catch (error) {
                return {success: false, message: `搜索API请求失败: ${error.message}`};
            }
        }
        
        async function testCourseDetail() {
            try {
                const response = await fetch(`${API_BASE}/api/course/1`);
                const data = await response.json();
                
                if (data.success && data.data.title) {
                    // 测试章节获取
                    const chaptersResponse = await fetch(`${API_BASE}/api/course/1/chapters`);
                    const chaptersData = await chaptersResponse.json();
                    
                    return {
                        success: true, 
                        message: `课程详情: ${data.data.title}, 章节数: ${chaptersData.data ? chaptersData.data.length : 0}`
                    };
                } else {
                    return {success: false, message: '获取课程详情失败'};
                }
            } catch (error) {
                return {success: false, message: `课程详情API请求失败: ${error.message}`};
            }
        }
        
        async function testCourseEnroll() {
            try {
                const response = await fetch(`${API_BASE}/api/course/1/enroll`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': STUDENT_ID
                    }
                });
                const data = await response.json();
                
                if (data.success || data.message.includes('已经报名')) {
                    return {success: true, message: '课程报名功能正常'};
                } else {
                    return {success: false, message: `报名失败: ${data.message}`};
                }
            } catch (error) {
                return {success: false, message: `报名API请求失败: ${error.message}`};
            }
        }
        
        async function testCourseCreate() {
            try {
                const courseData = {
                    title: "端到端测试课程 - " + new Date().toLocaleTimeString(),
                    description: "这是一个端到端测试创建的课程",
                    categoryId: 1,
                    difficultyLevel: "BEGINNER",
                    price: 0
                };
                
                const response = await fetch(`${API_BASE}/api/course`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify(courseData)
                });
                const data = await response.json();
                
                if (data.success && data.data.id) {
                    return {
                        success: true, 
                        message: `成功创建课程: ${data.data.title} (ID: ${data.data.id})`,
                        courseId: data.data.id
                    };
                } else {
                    return {success: false, message: `创建课程失败: ${data.message}`};
                }
            } catch (error) {
                return {success: false, message: `创建课程API请求失败: ${error.message}`};
            }
        }
        
        async function testTeacherManage() {
            try {
                const response = await fetch(`${API_BASE}/api/course/list?teacherOnly=true&page=1&size=10`, {
                    headers: {
                        'X-User-Id': TEACHER_ID
                    }
                });
                const data = await response.json();
                
                if (data.success && data.data.records) {
                    return {
                        success: true, 
                        message: `教师管理页面正常，共有${data.data.records.length}门课程`
                    };
                } else {
                    return {success: false, message: '教师课程管理功能失败'};
                }
            } catch (error) {
                return {success: false, message: `教师管理API请求失败: ${error.message}`};
            }
        }
        
        async function testCourseEdit(courseId) {
            try {
                const updateData = {
                    title: "端到端测试课程（已编辑）",
                    description: "这是编辑后的课程描述",
                    price: 99.00
                };
                
                const response = await fetch(`${API_BASE}/api/course/${courseId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify(updateData)
                });
                const data = await response.json();
                
                if (data.success) {
                    return {
                        success: true, 
                        message: `成功编辑课程: ${data.data.title}`
                    };
                } else {
                    return {success: false, message: `编辑课程失败: ${data.message}`};
                }
            } catch (error) {
                return {success: false, message: `编辑课程API请求失败: ${error.message}`};
            }
        }
        
        function generateTestReport(results) {
            const resultDiv = document.getElementById('e2eResult');
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            let reportHtml = `
                <div class="${successCount === totalCount ? 'success' : 'error'}">
                    <h3>📊 端到端测试报告</h3>
                    <p><strong>测试结果: ${successCount}/${totalCount} 通过</strong></p>
                    <ul>
            `;
            
            results.forEach(result => {
                reportHtml += `
                    <li>
                        ${result.success ? '✅' : '❌'} 
                        <strong>${result.step}</strong>: ${result.message}
                    </li>
                `;
            });
            
            reportHtml += `
                    </ul>
                    <p><strong>测试完成时间:</strong> ${new Date().toLocaleString()}</p>
                </div>
            `;
            
            resultDiv.innerHTML = reportHtml;
        }
        
        function openCoursesPage() {
            window.open(`${FRONTEND_BASE}/courses`, '_blank');
            document.getElementById('manualResult').innerHTML = 
                '<div class="success">✅ 已打开课程列表页面，请手动验证搜索、筛选、分页功能</div>';
        }
        
        function openCourseDetail() {
            window.open(`${FRONTEND_BASE}/course/1`, '_blank');
            document.getElementById('manualResult').innerHTML = 
                '<div class="success">✅ 已打开课程详情页面，请手动验证课程信息、章节列表、报名功能</div>';
        }
        
        function openTeacherManage() {
            window.open(`${FRONTEND_BASE}/course/manage`, '_blank');
            document.getElementById('manualResult').innerHTML = 
                '<div class="success">✅ 已打开教师课程管理页面，请手动验证课程列表、编辑、删除功能</div>';
        }
        
        function openCourseCreate() {
            window.open(`${FRONTEND_BASE}/course/edit`, '_blank');
            document.getElementById('manualResult').innerHTML = 
                '<div class="success">✅ 已打开课程创建页面，请手动验证表单填写、章节管理、保存功能</div>';
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            document.getElementById('e2eResult').innerHTML = 
                '<div class="info">💡 点击"运行完整端到端测试"按钮开始自动化测试，或使用下方按钮进行手动测试</div>';
        };
    </script>
</body>
</html>
