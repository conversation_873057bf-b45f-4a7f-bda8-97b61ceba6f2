
> frontend@0.1.0 serve
> vue-cli-service serve

 INFO  Starting development server...
[3%] setup (watch run)
[2K[1A[2K[G[3%] setup (watch run vue-loader-plugin)
[2K[1A[2K[G[3%] setup (watch run ESLintWebpackPlugin_1)
[2K[1A[2K[G[3%] setup (watch run webpack-dev-middleware)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[4%] setup (normal module factory CaseSensitivePathsPlugin)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[5%] setup (context module factory)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[6%] setup (before compile ProgressPlugin)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[7%] setup (compile ExternalsPlugin)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[8%] setup (compilation HtmlWebpackPlugin)
[2K[1A[2K[G[8%] setup (compilation CopyPlugin)
[2K[1A[2K[G[8%] setup (compilation ArrayPushCallbackChunkFormatPlugin)
[2K[1A[2K[G[8%] setup (compilation JsonpChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation StartupChunkDependenciesPlugin)
[2K[1A[2K[G[8%] setup (compilation ImportScriptsChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation WorkerPlugin)
[2K[1A[2K[G[8%] setup (compilation SplitChunksPlugin)
[2K[1A[2K[G[8%] setup (compilation ResolverCachePlugin)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation vue-loader-plugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ExternalsPlugin)
[2K[1A[2K[G[9%] setup (compilation ChunkPrefetchPreloadPlugin)
[2K[1A[2K[G[9%] setup (compilation ModuleInfoHeaderPlugin)
[2K[1A[2K[G[9%] setup (compilation EvalSourceMapDevToolPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation JsonModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AssetModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation RuntimePlugin)
[2K[1A[2K[G[9%] setup (compilation InferAsyncModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation DataUriPlugin)
[2K[1A[2K[G[9%] setup (compilation FileUriPlugin)
[2K[1A[2K[G[9%] setup (compilation CompatibilityPlugin)
[2K[1A[2K[G[9%] setup (compilation HarmonyModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AMDPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireJsStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation CommonJsPlugin)
[2K[1A[2K[G[9%] setup (compilation LoaderPlugin)
[2K[1A[2K[G[9%] setup (compilation NodeStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation APIPlugin)
[2K[1A[2K[G[9%] setup (compilation ExportsInfoApiPlugin)
[2K[1A[2K[G[9%] setup (compilation WebpackIsIncludedPlugin)
[2K[1A[2K[G[9%] setup (compilation ConstPlugin)
[2K[1A[2K[G[9%] setup (compilation UseStrictPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireIncludePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireEnsurePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireContextPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaContextPlugin)
[2K[1A[2K[G[9%] setup (compilation SystemPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaPlugin)
[2K[1A[2K[G[9%] setup (compilation URLPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsFactoryPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPresetPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPrinterPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptMetaInfoPlugin)
[2K[1A[2K[G[9%] setup (compilation EnsureChunkConditionsPlugin)
[2K[1A[2K[G[9%] setup (compilation RemoveEmptyChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation MergeDuplicateChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation SideEffectsFlagPlugin)
[2K[1A[2K[G[9%] setup (compilation FlagDependencyExportsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedModuleIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedChunkIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation TemplatedPathPlugin)
[2K[1A[2K[G[9%] setup (compilation RecordIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ProvidePlugin)
[2K[1A[2K[G[9%] setup (compilation HotModuleReplacementPlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation ESLintWebpackPlugin_1)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[10%] building 
[2K[1A[2K[G[10%] building (0/0 modules)
[2K[1A[2K[G[10%] building (import loader babel-loader/lib/index.js)
[2K[1A[2K[G[10%] building (0/2 modules)
[2K[1A[2K[G[24%] building (1/3 modules)
[2K[1A[2K[G[24%] building (import loader vue-loader/dist/index.js)
[2K[1A[2K[G[24%] building (3/16 modules)
[2K[1A[2K[G[24%] building (import loader vue-loader/dist/pitcher.js)
[2K[1A[2K[G[24%] building (7/34 modules)
[2K[1A[2K[G[24%] building (import loader vue-loader/dist/templateLoader.js)
[2K[1A[2K[G[24%] building (7/34 modules)
[2K[1A[2K[G[24%] building (import loader vue-style-loader/index.js)
[2K[1A[2K[G[24%] building (import loader css-loader/dist/cjs.js)
[2K[1A[2K[G[24%] building (import loader postcss-loader/dist/cjs.js)
[2K[1A[2K[G[24%] building (13/39 modules)
[2K[1A[2K[G[24%] building (import loader vue-loader/dist/stylePostLoader.js)
[2K[1A[2K[G[24%] building (13/74 modules)
[2K[1A[2K[G[38%] building (60/211 modules)
[2K[1A[2K[G[51%] building (197/305 modules)
[2K[1A[2K[G[65%] building (406/406 modules)
[2K[1A[2K[G[65%] building 
[2K[1A[2K[G[69%] building (finish)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[70%] sealing (finish module graph ResolverCachePlugin)
[2K[1A[2K[G[70%] sealing (finish module graph InferAsyncModulesPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph FlagDependencyExportsPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph ESLintWebpackPlugin_1)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (plugins WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[71%] sealing (dependencies optimization SideEffectsFlagPlugin)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[72%] sealing (after dependencies optimization)
[2K[1A[2K[G[73%] sealing (chunk graph)
[2K[1A[2K[G[73%] sealing (after chunk graph)
[2K[1A[2K[G[74%] sealing (optimizing)
[2K[1A[2K[G[74%] sealing (module optimization)
[2K[1A[2K[G[75%] sealing (after module optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization EnsureChunkConditionsPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization MergeDuplicateChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization SplitChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (after chunk optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization PersistentChildCompilerSinglet
onPlugin)
[2K[1A[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (after module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (chunk modules optimization)
[2K[1A[2K[G[79%] sealing (after chunk modules optimization)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (module reviving RecordIdsPlugin)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (before module ids)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[81%] sealing (module ids NamedModuleIdsPlugin)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (chunk reviving RecordIdsPlugin)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (before chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids NamedChunkIdsPlugin)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[85%] sealing (chunk id optimization)
[2K[1A[2K[G[85%] sealing (after chunk id optimization)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[86%] sealing (record modules RecordIdsPlugin)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (record chunks RecordIdsPlugin)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (module hashing)
[2K[1A[2K[G[88%] sealing (code generation)
[2K[1A[2K[G[89%] sealing (runtime requirements)
[2K[1A[2K[G[89%] sealing (hashing)
[2K[1A[2K[G[90%] sealing (after hashing)
[2K[1A[2K[G[91%] sealing (record hash)
[2K[1A[2K[G[91%] sealing (module assets processing)
[2K[1A[2K[G[92%] sealing (chunk assets processing)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[92%] sealing (asset processing copy-webpack-plugin)
[2K[1A[2K[G[92%] sealing (asset processing HotModuleReplacementPlugin)
[2K[1A[2K[G[92%] sealing (asset processing ESLintWebpackPlugin_1)
[2K[1A[2K[G[92%] sealing (asset processing PersistentChildCompilerSingletonPlugin)
[2K[1A[2K[G[92%] sealing (asset processing HtmlWebpackPlugin)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[93%] sealing (after asset optimization)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (recording HotModuleReplacementPlugin)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (after seal)
[2K[1A[2K[G[95%] emitting (emit)
[2K[1A[2K[G[98%] emitting (after emit)
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[G WARNING  Compiled with 1 warning6:46:34 PM

[eslint] 
/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/auth.js
  45:20  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/course.js
  98:24  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/learning.js
  76:27  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/recommendation.js
  38:26  warning  'commit' is defined but never used  no-unused-vars

✖ 4 problems (0 errors, 4 warnings)


You may use special comments to disable some warnings.
Use // eslint-disable-next-line to ignore the next line.
Use /* eslint-disable */ to ignore all warnings in a file.
Build finished at 18:46:34 by 0.000s
[2K[1A[2K[G
  App running at:
  - Local:   http://localhost:8080/ 
  - Network: http://*************:8080/

  Note that the development build is not optimized.
  To create a production build, run npm run build.

Build finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[GBuild finished at 18:46:34 by 0.000s
[2K[1A[2K[G WAIT  Compiling...6:49:11 PM

Compiling...

[2K[1A[2K[G[1%] cache (end idle)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[3%] setup (watch run vue-loader-plugin)
[2K[1A[2K[G[3%] setup (watch run ESLintWebpackPlugin_1)
[2K[1A[2K[G[3%] setup (watch run webpack-dev-middleware)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[4%] setup (normal module factory CaseSensitivePathsPlugin)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[5%] setup (context module factory)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[6%] setup (before compile ProgressPlugin)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[7%] setup (compile ExternalsPlugin)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[8%] setup (compilation HtmlWebpackPlugin)
[2K[1A[2K[G[8%] setup (compilation CopyPlugin)
[2K[1A[2K[G[8%] setup (compilation ArrayPushCallbackChunkFormatPlugin)
[2K[1A[2K[G[8%] setup (compilation JsonpChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation StartupChunkDependenciesPlugin)
[2K[1A[2K[G[8%] setup (compilation ImportScriptsChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation WorkerPlugin)
[2K[1A[2K[G[8%] setup (compilation SplitChunksPlugin)
[2K[1A[2K[G[8%] setup (compilation ResolverCachePlugin)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation vue-loader-plugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ExternalsPlugin)
[2K[1A[2K[G[9%] setup (compilation ChunkPrefetchPreloadPlugin)
[2K[1A[2K[G[9%] setup (compilation ModuleInfoHeaderPlugin)
[2K[1A[2K[G[9%] setup (compilation EvalSourceMapDevToolPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation JsonModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AssetModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation RuntimePlugin)
[2K[1A[2K[G[9%] setup (compilation InferAsyncModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation DataUriPlugin)
[2K[1A[2K[G[9%] setup (compilation FileUriPlugin)
[2K[1A[2K[G[9%] setup (compilation CompatibilityPlugin)
[2K[1A[2K[G[9%] setup (compilation HarmonyModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AMDPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireJsStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation CommonJsPlugin)
[2K[1A[2K[G[9%] setup (compilation LoaderPlugin)
[2K[1A[2K[G[9%] setup (compilation NodeStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation APIPlugin)
[2K[1A[2K[G[9%] setup (compilation ExportsInfoApiPlugin)
[2K[1A[2K[G[9%] setup (compilation WebpackIsIncludedPlugin)
[2K[1A[2K[G[9%] setup (compilation ConstPlugin)
[2K[1A[2K[G[9%] setup (compilation UseStrictPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireIncludePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireEnsurePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireContextPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaContextPlugin)
[2K[1A[2K[G[9%] setup (compilation SystemPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaPlugin)
[2K[1A[2K[G[9%] setup (compilation URLPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsFactoryPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPresetPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPrinterPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptMetaInfoPlugin)
[2K[1A[2K[G[9%] setup (compilation EnsureChunkConditionsPlugin)
[2K[1A[2K[G[9%] setup (compilation RemoveEmptyChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation MergeDuplicateChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation SideEffectsFlagPlugin)
[2K[1A[2K[G[9%] setup (compilation FlagDependencyExportsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedModuleIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedChunkIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation TemplatedPathPlugin)
[2K[1A[2K[G[9%] setup (compilation RecordIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ProvidePlugin)
[2K[1A[2K[G[9%] setup (compilation HotModuleReplacementPlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation ESLintWebpackPlugin_1)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[10%] building 
[2K[1A[2K[G[10%] building (0/0 modules)
[2K[1A[2K[G[24%] building (1/4 modules)
[2K[1A[2K[G[38%] building (65/19 modules)
[2K[1A[2K[G[51%] building (140/23 modules)
[2K[1A[2K[G[51%] building (import loader babel-loader/lib/index.js)
[2K[1A[2K[G[51%] building (316/49 modules)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/index.js)
[2K[1A[2K[G[51%] building (360/142 modules)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/pitcher.js)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/templateLoader.js)
[2K[1A[2K[G[51%] building (404/145 modules)
[2K[1A[2K[G[51%] building (import loader vue-style-loader/index.js)
[2K[1A[2K[G[51%] building (import loader css-loader/dist/cjs.js)
[2K[1A[2K[G[51%] building (import loader postcss-loader/dist/cjs.js)
[2K[1A[2K[G[51%] building (404/145 modules)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/stylePostLoader.js)
[2K[1A[2K[G[51%] building (404/153 modules)
[2K[1A[2K[G[65%] building (422/167 modules)
[2K[1A[2K[G[65%] building 
[2K[1A[2K[G[69%] building (finish)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[70%] sealing (finish module graph ResolverCachePlugin)
[2K[1A[2K[G[70%] sealing (finish module graph InferAsyncModulesPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph FlagDependencyExportsPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph ESLintWebpackPlugin_1)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (plugins WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[71%] sealing (dependencies optimization SideEffectsFlagPlugin)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[72%] sealing (after dependencies optimization)
[2K[1A[2K[G[73%] sealing (chunk graph)
[2K[1A[2K[G[73%] sealing (after chunk graph)
[2K[1A[2K[G[74%] sealing (optimizing)
[2K[1A[2K[G[74%] sealing (module optimization)
[2K[1A[2K[G[75%] sealing (after module optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization EnsureChunkConditionsPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization MergeDuplicateChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization SplitChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (after chunk optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization PersistentChildCompilerSinglet
onPlugin)
[2K[1A[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (after module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (chunk modules optimization)
[2K[1A[2K[G[79%] sealing (after chunk modules optimization)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (module reviving RecordIdsPlugin)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (before module ids)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[81%] sealing (module ids NamedModuleIdsPlugin)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (chunk reviving RecordIdsPlugin)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (before chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids NamedChunkIdsPlugin)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[85%] sealing (chunk id optimization)
[2K[1A[2K[G[85%] sealing (after chunk id optimization)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[86%] sealing (record modules RecordIdsPlugin)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (record chunks RecordIdsPlugin)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (module hashing)
[2K[1A[2K[G[88%] sealing (code generation)
[2K[1A[2K[G[89%] sealing (runtime requirements)
[2K[1A[2K[G[89%] sealing (hashing)
[2K[1A[2K[G[90%] sealing (after hashing)
[2K[1A[2K[G[91%] sealing (record hash)
[2K[1A[2K[G[91%] sealing (module assets processing)
[2K[1A[2K[G[92%] sealing (chunk assets processing)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[92%] sealing (asset processing copy-webpack-plugin)
[2K[1A[2K[G[92%] sealing (asset processing HotModuleReplacementPlugin)
[2K[1A[2K[G[92%] sealing (asset processing ESLintWebpackPlugin_1)
[2K[1A[2K[G[92%] sealing (asset processing PersistentChildCompilerSingletonPlugin)
[2K[1A[2K[G[92%] sealing (asset processing HtmlWebpackPlugin)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[93%] sealing (after asset optimization)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (recording HotModuleReplacementPlugin)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (after seal)
[2K[1A[2K[G[95%] emitting (emit)
[2K[1A[2K[G[98%] emitting (after emit)
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[G WARNING  Compiled with 1 warning6:49:12 PM

[eslint] 
/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/auth.js
  45:20  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/course.js
  98:24  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/learning.js
  76:27  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/recommendation.js
  38:26  warning  'commit' is defined but never used  no-unused-vars

✖ 4 problems (0 errors, 4 warnings)


You may use special comments to disable some warnings.
Use // eslint-disable-next-line to ignore the next line.
Use /* eslint-disable */ to ignore all warnings in a file.
Build finished at 18:49:12 by 0.000s
[2K[1A[2K[G
  App running at:
  - Local:   http://localhost:8080/ 
  - Network: http://*************:8080/

Build finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:12 by 0.000s
[2K[1A[2K[G WAIT  Compiling...6:49:20 PM

Compiling...

[2K[1A[2K[G[1%] cache (end idle)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[3%] setup (watch run vue-loader-plugin)
[2K[1A[2K[G[3%] setup (watch run ESLintWebpackPlugin_1)
[2K[1A[2K[G[3%] setup (watch run webpack-dev-middleware)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[4%] setup (normal module factory CaseSensitivePathsPlugin)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[5%] setup (context module factory)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[6%] setup (before compile ProgressPlugin)
[2K[1A[2K[G[6%] setup (before compile)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[7%] setup (compile ExternalsPlugin)
[2K[1A[2K[G[7%] setup (compile)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[8%] setup (compilation HtmlWebpackPlugin)
[2K[1A[2K[G[8%] setup (compilation CopyPlugin)
[2K[1A[2K[G[8%] setup (compilation ArrayPushCallbackChunkFormatPlugin)
[2K[1A[2K[G[8%] setup (compilation JsonpChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation StartupChunkDependenciesPlugin)
[2K[1A[2K[G[8%] setup (compilation ImportScriptsChunkLoadingPlugin)
[2K[1A[2K[G[8%] setup (compilation WorkerPlugin)
[2K[1A[2K[G[8%] setup (compilation SplitChunksPlugin)
[2K[1A[2K[G[8%] setup (compilation ResolverCachePlugin)
[2K[1A[2K[G[8%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[9%] setup (compilation vue-loader-plugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ExternalsPlugin)
[2K[1A[2K[G[9%] setup (compilation ChunkPrefetchPreloadPlugin)
[2K[1A[2K[G[9%] setup (compilation ModuleInfoHeaderPlugin)
[2K[1A[2K[G[9%] setup (compilation EvalSourceMapDevToolPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation JsonModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AssetModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation RuntimePlugin)
[2K[1A[2K[G[9%] setup (compilation InferAsyncModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation DataUriPlugin)
[2K[1A[2K[G[9%] setup (compilation FileUriPlugin)
[2K[1A[2K[G[9%] setup (compilation CompatibilityPlugin)
[2K[1A[2K[G[9%] setup (compilation HarmonyModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation AMDPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireJsStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation CommonJsPlugin)
[2K[1A[2K[G[9%] setup (compilation LoaderPlugin)
[2K[1A[2K[G[9%] setup (compilation NodeStuffPlugin)
[2K[1A[2K[G[9%] setup (compilation APIPlugin)
[2K[1A[2K[G[9%] setup (compilation ExportsInfoApiPlugin)
[2K[1A[2K[G[9%] setup (compilation WebpackIsIncludedPlugin)
[2K[1A[2K[G[9%] setup (compilation ConstPlugin)
[2K[1A[2K[G[9%] setup (compilation UseStrictPlugin)
[2K[1A[2K[G[9%] setup (compilation RequireIncludePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireEnsurePlugin)
[2K[1A[2K[G[9%] setup (compilation RequireContextPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaContextPlugin)
[2K[1A[2K[G[9%] setup (compilation SystemPlugin)
[2K[1A[2K[G[9%] setup (compilation ImportMetaPlugin)
[2K[1A[2K[G[9%] setup (compilation URLPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsFactoryPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPresetPlugin)
[2K[1A[2K[G[9%] setup (compilation DefaultStatsPrinterPlugin)
[2K[1A[2K[G[9%] setup (compilation JavascriptMetaInfoPlugin)
[2K[1A[2K[G[9%] setup (compilation EnsureChunkConditionsPlugin)
[2K[1A[2K[G[9%] setup (compilation RemoveEmptyChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation MergeDuplicateChunksPlugin)
[2K[1A[2K[G[9%] setup (compilation SideEffectsFlagPlugin)
[2K[1A[2K[G[9%] setup (compilation FlagDependencyExportsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedModuleIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation NamedChunkIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation DefinePlugin)
[2K[1A[2K[G[9%] setup (compilation TemplatedPathPlugin)
[2K[1A[2K[G[9%] setup (compilation RecordIdsPlugin)
[2K[1A[2K[G[9%] setup (compilation WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[9%] setup (compilation EntryPlugin)
[2K[1A[2K[G[9%] setup (compilation ProvidePlugin)
[2K[1A[2K[G[9%] setup (compilation HotModuleReplacementPlugin)
[2K[1A[2K[G[9%] setup (compilation ProgressPlugin)
[2K[1A[2K[G[9%] setup (compilation ESLintWebpackPlugin_1)
[2K[1A[2K[G[9%] setup (compilation)
[2K[1A[2K[G[10%] building 
[2K[1A[2K[G[10%] building (0/0 modules)
[2K[1A[2K[G[24%] building (1/4 modules)
[2K[1A[2K[G[38%] building (64/28 modules)
[2K[1A[2K[G[51%] building (239/133 modules)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/index.js)
[2K[1A[2K[G[51%] building (403/146 modules)
[2K[1A[2K[G[51%] building (import loader babel-loader/lib/index.js)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/templateLoader.js)
[2K[1A[2K[G[51%] building (407/153 modules)
[2K[1A[2K[G[51%] building (import loader css-loader/dist/cjs.js)
[2K[1A[2K[G[51%] building (import loader vue-loader/dist/stylePostLoader.js)
[2K[1A[2K[G[51%] building (import loader postcss-loader/dist/cjs.js)
[2K[1A[2K[G[51%] building (407/154 modules)
[2K[1A[2K[G[65%] building (422/160 modules)
[2K[1A[2K[G[65%] building 
[2K[1A[2K[G[69%] building (finish)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[70%] sealing (finish module graph ResolverCachePlugin)
[2K[1A[2K[G[70%] sealing (finish module graph InferAsyncModulesPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph FlagDependencyExportsPlugin)
[2K[1A[2K[G[70%] sealing (finish module graph ESLintWebpackPlugin_1)
[2K[1A[2K[G[70%] sealing (finish module graph)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (plugins WarnCaseSensitiveModulesPlugin)
[2K[1A[2K[G[71%] sealing (plugins)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[71%] sealing (dependencies optimization SideEffectsFlagPlugin)
[2K[1A[2K[G[71%] sealing (dependencies optimization)
[2K[1A[2K[G[72%] sealing (after dependencies optimization)
[2K[1A[2K[G[73%] sealing (chunk graph)
[2K[1A[2K[G[73%] sealing (after chunk graph)
[2K[1A[2K[G[74%] sealing (optimizing)
[2K[1A[2K[G[74%] sealing (module optimization)
[2K[1A[2K[G[75%] sealing (after module optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (chunk optimization EnsureChunkConditionsPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization MergeDuplicateChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization SplitChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization RemoveEmptyChunksPlugin)
[2K[1A[2K[G[76%] sealing (chunk optimization)
[2K[1A[2K[G[76%] sealing (after chunk optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[77%] sealing (module and chunk tree optimization PersistentChildCompilerSinglet
onPlugin)
[2K[1A[2K[1A[2K[G[77%] sealing (module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (after module and chunk tree optimization)
[2K[1A[2K[G[78%] sealing (chunk modules optimization)
[2K[1A[2K[G[79%] sealing (after chunk modules optimization)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (module reviving RecordIdsPlugin)
[2K[1A[2K[G[80%] sealing (module reviving)
[2K[1A[2K[G[80%] sealing (before module ids)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[81%] sealing (module ids NamedModuleIdsPlugin)
[2K[1A[2K[G[81%] sealing (module ids)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[82%] sealing (module id optimization)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (chunk reviving RecordIdsPlugin)
[2K[1A[2K[G[83%] sealing (chunk reviving)
[2K[1A[2K[G[83%] sealing (before chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[84%] sealing (chunk ids NamedChunkIdsPlugin)
[2K[1A[2K[G[84%] sealing (chunk ids)
[2K[1A[2K[G[85%] sealing (chunk id optimization)
[2K[1A[2K[G[85%] sealing (after chunk id optimization)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[86%] sealing (record modules RecordIdsPlugin)
[2K[1A[2K[G[86%] sealing (record modules)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (record chunks RecordIdsPlugin)
[2K[1A[2K[G[87%] sealing (record chunks)
[2K[1A[2K[G[87%] sealing (module hashing)
[2K[1A[2K[G[88%] sealing (code generation)
[2K[1A[2K[G[89%] sealing (runtime requirements)
[2K[1A[2K[G[89%] sealing (hashing)
[2K[1A[2K[G[90%] sealing (after hashing)
[2K[1A[2K[G[91%] sealing (record hash)
[2K[1A[2K[G[91%] sealing (module assets processing)
[2K[1A[2K[G[92%] sealing (chunk assets processing)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[92%] sealing (asset processing copy-webpack-plugin)
[2K[1A[2K[G[92%] sealing (asset processing HotModuleReplacementPlugin)
[2K[1A[2K[G[92%] sealing (asset processing ESLintWebpackPlugin_1)
[2K[1A[2K[G[92%] sealing (asset processing PersistentChildCompilerSingletonPlugin)
[2K[1A[2K[G[92%] sealing (asset processing HtmlWebpackPlugin)
[2K[1A[2K[G[92%] sealing (asset processing)
[2K[1A[2K[G[93%] sealing (after asset optimization)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (recording HotModuleReplacementPlugin)
[2K[1A[2K[G[94%] sealing (recording)
[2K[1A[2K[G[94%] sealing (after seal)
[2K[1A[2K[G[95%] emitting (emit)
[2K[1A[2K[G[98%] emitting (after emit)
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[G WARNING  Compiled with 1 warning6:49:20 PM

[eslint] 
/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/auth.js
  45:20  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/course.js
  98:24  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/learning.js
  76:27  warning  'commit' is defined but never used  no-unused-vars

/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/frontend/src/store/modules/recommendation.js
  38:26  warning  'commit' is defined but never used  no-unused-vars

✖ 4 problems (0 errors, 4 warnings)


You may use special comments to disable some warnings.
Use // eslint-disable-next-line to ignore the next line.
Use /* eslint-disable */ to ignore all warnings in a file.
Build finished at 18:49:20 by 0.000s
[2K[1A[2K[G
  App running at:
  - Local:   http://localhost:8080/ 
  - Network: http://*************:8080/

Build finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[GBuild finished at 18:49:20 by 0.000s
[2K[1A[2K[G