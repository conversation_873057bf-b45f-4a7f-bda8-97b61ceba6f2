<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程详情页面测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>课程详情页面功能测试</h1>
    
    <div class="test-section">
        <h2>1. 课程详情API测试</h2>
        <button onclick="testCourseDetail()">测试课程详情</button>
        <div id="courseDetailResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 课程章节API测试</h2>
        <button onclick="testCourseChapters()">测试课程章节</button>
        <div id="chaptersResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 课程报名API测试</h2>
        <button onclick="testCourseEnroll()">测试课程报名</button>
        <div id="enrollResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 前端页面测试</h2>
        <button onclick="openCoursePage()">打开课程详情页面</button>
        <div id="pageResult" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082';
        
        async function testCourseDetail() {
            const resultDiv = document.getElementById('courseDetailResult');
            try {
                const response = await fetch(`${API_BASE}/api/course/1`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程详情API测试成功<br>
                            课程标题: ${data.data.title}<br>
                            讲师: ${data.data.teacherName || '未知'}<br>
                            价格: ${data.data.price === 0 ? '免费' : '¥' + data.data.price}<br>
                            学习人数: ${data.data.studentsCount || 0}人
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testCourseChapters() {
            const resultDiv = document.getElementById('chaptersResult');
            try {
                const response = await fetch(`${API_BASE}/api/course/1/chapters`);
                const data = await response.json();
                
                if (data.success) {
                    const chapters = data.data || [];
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程章节API测试成功<br>
                            章节数量: ${chapters.length}<br>
                            ${chapters.slice(0, 3).map((ch, i) => 
                                `第${i+1}章: ${ch.title} (${ch.durationMinutes || 0}分钟)`
                            ).join('<br>')}
                            ${chapters.length > 3 ? '<br>...' : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testCourseEnroll() {
            const resultDiv = document.getElementById('enrollResult');
            try {
                const response = await fetch(`${API_BASE}/api/course/1/enroll`, {
                    method: 'POST',
                    headers: {
                        'X-User-Id': '1',
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程报名API测试成功<br>
                            返回消息: ${data.data}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        function openCoursePage() {
            const resultDiv = document.getElementById('pageResult');
            window.open('http://localhost:8080/course/1', '_blank');
            resultDiv.innerHTML = `
                <div class="success">
                    ✅ 已在新窗口打开课程详情页面<br>
                    请检查页面是否正常显示课程信息和章节列表
                </div>
            `;
        }
        
        // 页面加载时自动运行所有测试
        window.onload = function() {
            setTimeout(() => {
                testCourseDetail();
                setTimeout(() => testCourseChapters(), 500);
                setTimeout(() => testCourseEnroll(), 1000);
            }, 500);
        };
    </script>
</body>
</html>
