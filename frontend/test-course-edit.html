<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程创建和编辑功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .course-data { border: 1px solid #ddd; margin: 10px 0; padding: 10px; background: #f8f9fa; }
        pre { background: #f1f1f1; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>课程创建和编辑功能测试</h1>
    
    <div class="test-section">
        <h2>1. 测试课程创建API</h2>
        <button onclick="testCreateCourse()">测试创建课程</button>
        <div id="createResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试课程编辑API</h2>
        <button onclick="testUpdateCourse()">测试更新课程</button>
        <div id="updateResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试获取课程详情（编辑时需要）</h2>
        <button onclick="testGetCourseForEdit()">测试获取课程详情</button>
        <div id="getResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 前端页面测试</h2>
        <button onclick="openCreatePage()">打开课程创建页面</button>
        <button onclick="openEditPage()">打开课程编辑页面</button>
        <div id="pageResult" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082';
        const TEACHER_ID = '1';
        let createdCourseId = null;
        
        async function testCreateCourse() {
            const resultDiv = document.getElementById('createResult');
            try {
                const courseData = {
                    title: "测试课程创建 - " + new Date().toLocaleTimeString(),
                    description: "这是一个通过API创建的测试课程，用于验证课程创建功能",
                    categoryId: 1,
                    difficultyLevel: "BEGINNER",
                    price: 0,
                    status: "DRAFT"
                };
                
                const response = await fetch(`${API_BASE}/api/course`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify(courseData)
                });
                const data = await response.json();
                
                if (data.success) {
                    createdCourseId = data.data.id;
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程创建成功<br>
                            课程ID: ${data.data.id}<br>
                            课程标题: ${data.data.title}<br>
                            课程状态: ${data.data.status}<br>
                            创建时间: ${data.data.createdAt}
                            <div class="course-data">
                                <strong>完整课程数据:</strong>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testUpdateCourse() {
            const resultDiv = document.getElementById('updateResult');
            const courseId = createdCourseId || 1; // 使用刚创建的课程或默认课程1
            
            try {
                const updateData = {
                    title: "更新后的课程标题 - " + new Date().toLocaleTimeString(),
                    description: "这是更新后的课程描述，验证课程编辑功能",
                    categoryId: 2,
                    difficultyLevel: "INTERMEDIATE",
                    price: 99.00,
                    status: "PUBLISHED"
                };
                
                const response = await fetch(`${API_BASE}/api/course/${courseId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify(updateData)
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程更新成功<br>
                            课程ID: ${data.data.id}<br>
                            更新后标题: ${data.data.title}<br>
                            更新后状态: ${data.data.status}<br>
                            更新后价格: ¥${data.data.price}
                            <div class="course-data">
                                <strong>完整更新数据:</strong>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testGetCourseForEdit() {
            const resultDiv = document.getElementById('getResult');
            const courseId = createdCourseId || 1;
            
            try {
                const response = await fetch(`${API_BASE}/api/course/${courseId}`, {
                    headers: {
                        'X-User-Id': TEACHER_ID
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 获取课程详情成功<br>
                            课程ID: ${data.data.id}<br>
                            课程标题: ${data.data.title}<br>
                            课程描述: ${data.data.description}<br>
                            分类ID: ${data.data.categoryId}<br>
                            难度级别: ${data.data.difficultyLevel}<br>
                            价格: ¥${data.data.price}<br>
                            状态: ${data.data.status}
                            <div class="course-data">
                                <strong>完整课程数据（用于编辑表单填充）:</strong>
                                <pre>${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        function openCreatePage() {
            const resultDiv = document.getElementById('pageResult');
            window.open('http://localhost:8080/course/edit', '_blank');
            resultDiv.innerHTML = `
                <div class="success">
                    ✅ 已在新窗口打开课程创建页面<br>
                    请检查页面是否正常显示表单和所有字段
                </div>
            `;
        }
        
        function openEditPage() {
            const resultDiv = document.getElementById('pageResult');
            const courseId = createdCourseId || 1;
            window.open(`http://localhost:8080/course/edit/${courseId}`, '_blank');
            resultDiv.innerHTML = `
                <div class="success">
                    ✅ 已在新窗口打开课程编辑页面（课程ID: ${courseId}）<br>
                    请检查页面是否正常加载课程数据并填充表单
                </div>
            `;
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                testCreateCourse();
                setTimeout(() => testUpdateCourse(), 2000);
                setTimeout(() => testGetCourseForEdit(), 4000);
            }, 500);
        };
    </script>
</body>
</html>
