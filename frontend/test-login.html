<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录API测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="testlearning">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testUserProfile()">测试获取用户信息</button>
        <button onclick="testCORS()">测试CORS预检</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showResult('正在测试登录...', 'info');
            
            try {
                const response = await fetch('http://localhost:8090/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        usernameOrEmail: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    showResult(`登录成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`登录失败！\n\n错误信息:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`登录请求失败！\n\n错误信息:\n${error.message}`, 'error');
            }
        }

        async function testUserProfile() {
            if (!authToken) {
                showResult('请先登录获取token！', 'error');
                return;
            }
            
            showResult('正在获取用户信息...', 'info');
            
            try {
                const response = await fetch('http://localhost:8090/api/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`获取用户信息成功！\n\n用户数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`获取用户信息失败！\n\n错误信息:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`获取用户信息请求失败！\n\n错误信息:\n${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        async function testCORS() {
            showResult('正在测试CORS预检请求...', 'info');

            try {
                const response = await fetch('http://localhost:8090/api/user/login', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:8086',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });

                showResult(`CORS预检成功！\n\n状态码: ${response.status}\n\n响应头:\n${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`, 'success');
            } catch (error) {
                showResult(`CORS预检失败！\n\n错误信息:\n${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }

        // 页面加载时显示说明
        window.onload = function() {
            showResult('点击"测试登录"按钮开始测试API连接', 'info');
        };
    </script>
</body>
</html>
