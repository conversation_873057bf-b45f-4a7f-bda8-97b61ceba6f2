<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习仪表板功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f1f1f1; padding: 10px; overflow-x: auto; font-size: 12px; }
        .api-data { border: 1px solid #ddd; margin: 10px 0; padding: 10px; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>学习仪表板功能测试</h1>
    <p>测试学习仪表板页面的API集成和数据显示功能</p>
    
    <div class="test-section">
        <h2>API测试</h2>
        <button onclick="testDashboardAPI()">测试仪表板API</button>
        <button onclick="testStatisticsAPI()">测试统计API</button>
        <button onclick="createTestData()">创建测试数据</button>
        <div id="apiResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>页面测试</h2>
        <button onclick="openDashboardPage()">打开学习仪表板页面</button>
        <button onclick="testResponsive()">测试响应式设计</button>
        <div id="pageResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>数据展示测试</h2>
        <div id="dashboardData" class="api-data">
            <h3>仪表板数据</h3>
            <div id="dashboardContent">点击"测试仪表板API"加载数据</div>
        </div>
        <div id="statisticsData" class="api-data">
            <h3>统计数据</h3>
            <div id="statisticsContent">点击"测试统计API"加载数据</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8083';
        const FRONTEND_BASE = 'http://localhost:8080';
        const USER_ID = '1';
        
        async function testDashboardAPI() {
            const resultDiv = document.getElementById('apiResult');
            const contentDiv = document.getElementById('dashboardContent');
            
            resultDiv.innerHTML = '<div class="info">🔄 正在测试仪表板API...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/learning/dashboard?userId=${USER_ID}`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ 仪表板API测试成功</div>';
                    contentDiv.innerHTML = `
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        <div style="margin-top: 10px;">
                            <strong>数据解析:</strong><br>
                            • 已注册课程: ${data.data.totalEnrolledCourses}<br>
                            • 学习中课程: ${data.data.inProgressCourses}<br>
                            • 已完成课程: ${data.data.completedCourses}<br>
                            • 已完成章节: ${data.data.completedChapters}<br>
                            • 学习时长: ${data.data.totalLearningHours}小时<br>
                            • 最近注册: ${data.data.recentEnrollments.length}条记录
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 仪表板API测试失败: ${data.message}</div>`;
                    contentDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ API请求失败: ${error.message}</div>`;
                contentDiv.innerHTML = `<div class="error">请求错误: ${error.message}</div>`;
            }
        }
        
        async function testStatisticsAPI() {
            const resultDiv = document.getElementById('apiResult');
            const contentDiv = document.getElementById('statisticsContent');
            
            resultDiv.innerHTML = '<div class="info">🔄 正在测试统计API...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/learning/statistics?userId=${USER_ID}`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ 统计API测试成功</div>';
                    contentDiv.innerHTML = `
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        <div style="margin-top: 10px;">
                            <strong>统计数据解析:</strong><br>
                            • 平均进度: ${data.data.averageProgress}%<br>
                            • 总课程数: ${data.data.totalCourses}<br>
                            • 总章节数: ${data.data.totalChapters}<br>
                            • 已完成章节: ${data.data.completedChapters}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 统计API测试失败: ${data.message}</div>`;
                    contentDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ API请求失败: ${error.message}</div>`;
                contentDiv.innerHTML = `<div class="error">请求错误: ${error.message}</div>`;
            }
        }
        
        async function createTestData() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在创建测试数据...</div>';
            
            try {
                // 1. 注册课程
                const enrollResponse = await fetch(`${API_BASE}/api/learning/enroll?userId=${USER_ID}&courseId=2`, {
                    method: 'POST'
                });
                const enrollData = await enrollResponse.json();
                
                // 2. 更新学习进度
                const progressResponse = await fetch(`${API_BASE}/api/learning/progress`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId: parseInt(USER_ID),
                        courseId: 1,
                        chapterId: 1,
                        watchDurationSeconds: 3600,
                        lastPositionSeconds: 3600
                    })
                });
                const progressData = await progressResponse.json();
                
                // 3. 完成章节
                const completeResponse = await fetch(`${API_BASE}/api/learning/progress/complete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId: parseInt(USER_ID),
                        courseId: 1,
                        chapterId: 1
                    })
                });
                const completeData = await completeResponse.json();
                
                // 4. 提交测验
                const quizResponse = await fetch(`${API_BASE}/api/learning/quiz/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId: parseInt(USER_ID),
                        chapterId: 1,
                        score: 88.5,
                        totalScore: 100.0
                    })
                });
                const quizData = await quizResponse.json();
                
                if (enrollData.success && progressData.success && completeData.success && quizData.success) {
                    resultDiv.innerHTML = '<div class="success">✅ 测试数据创建成功！现在可以测试仪表板功能了</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 部分测试数据创建失败，请检查API响应</div>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 创建测试数据失败: ${error.message}</div>`;
            }
        }
        
        function openDashboardPage() {
            window.open(`${FRONTEND_BASE}/learning/dashboard`, '_blank');
            document.getElementById('pageResult').innerHTML = 
                '<div class="success">✅ 已打开学习仪表板页面，请手动验证以下功能：<br>' +
                '• 统计卡片显示<br>' +
                '• 最近学习记录<br>' +
                '• 进度条显示<br>' +
                '• 快速操作按钮<br>' +
                '• 响应式布局</div>';
        }
        
        function testResponsive() {
            const instructions = `
                <div class="info">
                    <h4>📱 响应式设计测试指南:</h4>
                    <ol>
                        <li>打开学习仪表板页面</li>
                        <li>调整浏览器窗口大小到不同尺寸</li>
                        <li>验证以下响应式特性：</li>
                        <ul>
                            <li>统计卡片在小屏幕上单列显示</li>
                            <li>课程卡片在移动端垂直排列</li>
                            <li>操作按钮在小屏幕上全宽显示</li>
                            <li>文字和间距适配不同屏幕</li>
                        </ul>
                    </ol>
                </div>
            `;
            document.getElementById('pageResult').innerHTML = instructions;
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            document.getElementById('apiResult').innerHTML = 
                '<div class="info">💡 点击上方按钮开始测试学习仪表板功能</div>';
        };
    </script>
</body>
</html>
