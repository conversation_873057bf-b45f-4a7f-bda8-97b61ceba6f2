<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师课程管理功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .course-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; }
    </style>
</head>
<body>
    <h1>教师课程管理功能测试</h1>
    
    <div class="test-section">
        <h2>1. 获取教师课程列表</h2>
        <button onclick="testGetTeacherCourses()">测试获取教师课程</button>
        <div id="teacherCoursesResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 课程更新功能</h2>
        <button onclick="testUpdateCourse()">测试更新课程</button>
        <div id="updateResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 课程状态管理</h2>
        <button onclick="testCourseStatus()">测试课程状态切换</button>
        <div id="statusResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 前端页面测试</h2>
        <button onclick="openManagePage()">打开教师课程管理页面</button>
        <div id="pageResult" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8082';
        const TEACHER_ID = '1';
        
        async function testGetTeacherCourses() {
            const resultDiv = document.getElementById('teacherCoursesResult');
            try {
                const response = await fetch(`${API_BASE}/api/course/list?teacherOnly=true&page=1&size=10`, {
                    headers: {
                        'X-User-Id': TEACHER_ID
                    }
                });
                const data = await response.json();
                
                if (data.success) {
                    const courses = data.data.records || [];
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 获取教师课程列表成功<br>
                            课程数量: ${courses.length}<br>
                            ${courses.map(course => `
                                <div class="course-item">
                                    <strong>${course.title}</strong><br>
                                    状态: ${course.status}<br>
                                    学生数: ${course.studentsCount || 0}人<br>
                                    章节数: ${course.chaptersCount || 0}个<br>
                                    价格: ${course.price === 0 ? '免费' : '¥' + course.price}
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testUpdateCourse() {
            const resultDiv = document.getElementById('updateResult');
            try {
                const updateData = {
                    title: "Java编程基础（测试更新）",
                    description: "这是一个测试更新的描述"
                };
                
                const response = await fetch(`${API_BASE}/api/course/1`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify(updateData)
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程更新成功<br>
                            更新后标题: ${data.data.title}<br>
                            更新后描述: ${data.data.description}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API返回失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testCourseStatus() {
            const resultDiv = document.getElementById('statusResult');
            try {
                // 先获取当前课程状态
                const getResponse = await fetch(`${API_BASE}/api/course/2`, {
                    headers: {
                        'X-User-Id': TEACHER_ID
                    }
                });
                const currentData = await getResponse.json();
                
                if (!currentData.success) {
                    resultDiv.innerHTML = `<div class="error">❌ 获取课程信息失败: ${currentData.message}</div>`;
                    return;
                }
                
                const currentStatus = currentData.data.status;
                const newStatus = currentStatus === 'PUBLISHED' ? 'DRAFT' : 'PUBLISHED';
                
                // 更新课程状态
                const updateResponse = await fetch(`${API_BASE}/api/course/2`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-Id': TEACHER_ID
                    },
                    body: JSON.stringify({ status: newStatus })
                });
                const updateData = await updateResponse.json();
                
                if (updateData.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 课程状态更新成功<br>
                            原状态: ${currentStatus}<br>
                            新状态: ${updateData.data.status}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 状态更新失败: ${updateData.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        function openManagePage() {
            const resultDiv = document.getElementById('pageResult');
            window.open('http://localhost:8080/course/manage', '_blank');
            resultDiv.innerHTML = `
                <div class="success">
                    ✅ 已在新窗口打开教师课程管理页面<br>
                    请检查页面是否正常显示课程列表和管理功能
                </div>
            `;
        }
        
        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                testGetTeacherCourses();
                setTimeout(() => testUpdateCourse(), 1000);
                setTimeout(() => testCourseStatus(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
