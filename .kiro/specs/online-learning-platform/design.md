# 设计文档

## 概述

基于微服务架构的在线学习平台采用前后端分离的设计模式，后端使用SpringCloud Alibaba构建微服务集群，前端使用Vue.js构建单页应用。系统通过API Gateway统一对外提供服务，各微服务通过Nacos进行服务注册与发现，使用MySQL作为主要数据存储。

## 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 前端应用]
    end
    
    subgraph "网关层"
        B[Spring Cloud Gateway]
    end
    
    subgraph "微服务层"
        C[用户服务<br/>user-service]
        D[课程服务<br/>course-service]
        E[学习服务<br/>learning-service]
        F[推荐服务<br/>recommendation-service]
        G[讨论服务<br/>discussion-service]
    end
    
    subgraph "基础设施层"
        H[Nacos<br/>服务注册发现]
        I[MySQL 8.4.6<br/>主数据库]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    
    C --> H
    D --> H
    E --> H
    F --> H
    G --> H
    
    C --> I
    D --> I
    E --> I
    F --> I
    G --> I
```

### 微服务划分

1. **用户服务 (user-service)**
   - 端口：8081
   - 职责：用户注册、登录、权限管理、个人信息管理
   - 数据库：user_db

2. **课程服务 (course-service)**
   - 端口：8082
   - 职责：课程创建、编辑、发布、搜索、分类管理
   - 数据库：course_db

3. **学习服务 (learning-service)**
   - 端口：8083
   - 职责：学习进度跟踪、成绩管理、学习统计
   - 数据库：learning_db

4. **推荐服务 (recommendation-service)**
   - 端口：8084
   - 职责：课程推荐算法、用户行为分析、推荐结果生成
   - 数据库：recommendation_db

5. **讨论服务 (discussion-service)**
   - 端口：8085
   - 职责：课程讨论、问答管理、互动功能
   - 数据库：discussion_db

## 组件和接口设计

### 前端组件架构

```
src/
├── components/           # 公共组件
│   ├── common/          # 通用组件
│   ├── layout/          # 布局组件
│   └── business/        # 业务组件
├── views/               # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── course/         # 课程相关页面
│   ├── learning/       # 学习相关页面
│   ├── discussion/     # 讨论相关页面
│   └── profile/        # 个人中心页面
├── router/             # 路由配置
├── store/              # Vuex状态管理
├── api/                # API接口封装
├── utils/              # 工具函数
└── assets/             # 静态资源
```

### 后端服务接口设计

#### 用户服务 API

```
POST /api/user/register          # 用户注册
POST /api/user/login             # 用户登录
GET  /api/user/profile           # 获取用户信息
PUT  /api/user/profile           # 更新用户信息
POST /api/user/reset-password    # 重置密码
GET  /api/user/verify-email      # 邮箱验证
```

#### 课程服务 API

```
GET    /api/course/list          # 获取课程列表
GET    /api/course/{id}          # 获取课程详情
POST   /api/course               # 创建课程
PUT    /api/course/{id}          # 更新课程
DELETE /api/course/{id}          # 删除课程
GET    /api/course/search        # 搜索课程
POST   /api/course/{id}/enroll   # 报名课程
```

#### 学习服务 API

```
GET  /api/learning/progress/{courseId}    # 获取学习进度
POST /api/learning/progress               # 更新学习进度
GET  /api/learning/dashboard              # 学习仪表板
POST /api/learning/quiz/submit            # 提交测验
GET  /api/learning/statistics             # 学习统计
```

#### 推荐服务 API

```
GET  /api/recommendation/courses          # 获取课程推荐
POST /api/recommendation/feedback         # 推荐反馈
GET  /api/recommendation/related/{courseId} # 相关课程推荐
```

#### 讨论服务 API

```
GET    /api/discussion/{courseId}         # 获取课程讨论
POST   /api/discussion                    # 创建讨论
POST   /api/discussion/{id}/reply         # 回复讨论
PUT    /api/discussion/{id}/like          # 点赞讨论
GET    /api/discussion/search             # 搜索讨论
```

## 数据模型设计

### 用户服务数据模型

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('STUDENT', 'TEACHER', 'ADMIN') DEFAULT 'STUDENT',
    avatar_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 用户资料表
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    real_name VARCHAR(100),
    phone VARCHAR(20),
    bio TEXT,
    birth_date DATE,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 课程服务数据模型

```sql
-- 课程表
CREATE TABLE courses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    teacher_id BIGINT NOT NULL,
    category_id BIGINT,
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED'),
    cover_image VARCHAR(255),
    price DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 课程章节表
CREATE TABLE course_chapters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    course_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content_type ENUM('VIDEO', 'DOCUMENT', 'QUIZ'),
    content_url VARCHAR(500),
    duration_minutes INT,
    order_index INT,
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- 课程分类表
CREATE TABLE course_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    FOREIGN KEY (parent_id) REFERENCES course_categories(id)
);
```

### 学习服务数据模型

```sql
-- 课程注册表
CREATE TABLE course_enrollments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    UNIQUE KEY unique_enrollment (user_id, course_id)
);

-- 学习进度表
CREATE TABLE learning_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    watch_duration_seconds INT DEFAULT 0,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL,
    UNIQUE KEY unique_progress (user_id, course_id, chapter_id)
);

-- 测验记录表
CREATE TABLE quiz_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    score DECIMAL(5,2),
    total_score DECIMAL(5,2),
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 推荐服务数据模型

```sql
-- 用户行为表
CREATE TABLE user_behaviors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    behavior_type ENUM('VIEW', 'ENROLL', 'COMPLETE', 'RATE'),
    behavior_value DECIMAL(3,1), -- 用于评分等数值行为
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 推荐结果表
CREATE TABLE recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    score DECIMAL(5,2),
    reason VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 讨论服务数据模型

```sql
-- 讨论主题表
CREATE TABLE discussions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    course_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    likes_count INT DEFAULT 0,
    replies_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 讨论回复表
CREATE TABLE discussion_replies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    discussion_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    parent_reply_id BIGINT NULL,
    likes_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (discussion_id) REFERENCES discussions(id),
    FOREIGN KEY (parent_reply_id) REFERENCES discussion_replies(id)
);
```

## 错误处理

### 统一错误响应格式

```json
{
    "success": false,
    "code": "ERROR_CODE",
    "message": "错误描述信息",
    "data": null,
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误码定义

- **1000-1999**: 用户服务错误
  - 1001: 用户不存在
  - 1002: 密码错误
  - 1003: 邮箱已存在
  - 1004: 权限不足

- **2000-2999**: 课程服务错误
  - 2001: 课程不存在
  - 2002: 课程已下架
  - 2003: 重复报名

- **3000-3999**: 学习服务错误
  - 3001: 未报名课程
  - 3002: 章节不存在
  - 3003: 测验已提交

- **4000-4999**: 推荐服务错误
  - 4001: 推荐数据不足
  - 4002: 算法计算失败

- **5000-5999**: 讨论服务错误
  - 5001: 讨论不存在
  - 5002: 无权限操作

### 异常处理策略

1. **基础异常处理**: 捕获并返回友好的错误信息
2. **日志记录**: 记录关键异常到控制台日志
3. **前端错误提示**: 在页面上显示用户友好的错误信息
4. **数据库异常**: 处理连接失败和SQL执行异常

## 测试策略

### 功能测试

- **手动测试**: 每次代码更改后进行前后端功能验证
- **API测试**: 使用Postman或浏览器测试所有API接口
- **前端测试**: 在浏览器中测试所有页面功能和交互

### 部署验证

- **编译验证**: 每次更改后确保前后端项目能正常编译
- **启动验证**: 确保所有微服务能正常启动并注册到Nacos
- **连通性测试**: 验证前端能正常调用后端API
- **数据库连接**: 验证各服务能正常连接MySQL数据库

### 持续验证原则

- 每次代码修改后立即编译运行
- 不积累错误，发现问题立即修复
- 前后端联调测试，确保接口对接正确
- 扎实实现每个功能模块