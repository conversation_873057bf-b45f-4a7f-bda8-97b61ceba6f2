-- 学习服务数据库表结构 (H2 Compatible)

-- 课程注册表
CREATE TABLE IF NOT EXISTS course_enrollments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00
);

-- 学习进度表
CREATE TABLE IF NOT EXISTS learning_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    watch_duration_seconds INT DEFAULT 0,
    last_position_seconds INT DEFAULT 0,
    completed_at TIMESTAMP NULL
);

-- 测验记录表
CREATE TABLE IF NOT EXISTS quiz_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    chapter_id BIGINT NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    total_score DECIMAL(5,2) NOT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO course_enrollments (user_id, course_id, progress_percentage) VALUES 
(1, 1, 25.50),
(1, 2, 0.00),
(2, 1, 75.00);

INSERT INTO learning_progress (user_id, course_id, chapter_id, completed, watch_duration_seconds, last_position_seconds) VALUES 
(1, 1, 1, true, 1800, 1800),
(1, 1, 2, false, 900, 900),
(2, 1, 1, true, 1500, 1500);

INSERT INTO quiz_records (user_id, chapter_id, score, total_score) VALUES 
(1, 1, 85.00, 100.00),
(1, 2, 78.50, 100.00),
(2, 1, 92.00, 100.00);