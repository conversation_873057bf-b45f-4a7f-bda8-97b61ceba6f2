server:
  port: 8083

spring:
  application:
    name: learning-service
  
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
    username: sa
    password: ''
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  sql:
    init:
      mode: always
  
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public

# mybatis-plus:
#   configuration:
#     map-underscore-to-camel-case: true
#     log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#   global-config:
#     db-config:
#       id-type: auto
#       logic-delete-field: deleted
#       logic-delete-value: 1
#       logic-not-delete-value: 0

logging:
  level:
    com.learningplatform.learning: debug