[INFO] Scanning for projects...
[INFO] 
[INFO] --------------< com.learningplatform:discussion-service >---------------
[INFO] Building Discussion Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ discussion-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ discussion-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ discussion-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ discussion-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ discussion-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ discussion-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ discussion-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:02:03.174+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:02:03.192+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:02:03.195+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Starting DiscussionServiceApplication using Java 23.0.2 with PID 19638 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service)
[2m2025-08-01T18:02:03.195+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:02:03.196+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:02:03.350+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.openfeign.FeignClientsRegistrar$1 [0;39m [2m:[0;39m Identified candidate component class: file [/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/discussion-service/target/classes/com/learningplatform/discussion/feign/UserServiceClient.class]
[2m2025-08-01T18:02:03.449+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m Generating bean factory id from names: [applicationAvailability, applicationTaskExecutor, basicErrorController, beanNameHandlerMapping, beanNameViewResolver, characterEncodingFilter, com.alibaba.cloud.nacos.NacosServiceAutoConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration, com.alibaba.cloud.nacos.discovery.NacosDiscoveryHeartBeatConfiguration, com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration, com.alibaba.cloud.nacos.util.UtilIPv6AutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.learningplatform.discussion.DiscussionServiceApplication#MapperScannerRegistrar#0, com.learningplatform.discussion.feign.UserServiceClient, compositeCompatibilityVerifier, compositeDiscoveryClient, configDataContextRefresher, configurationPropertiesBeans, configurationPropertiesRebinder, conventionErrorViewResolver, dataInitController, dataSource, dataSourceScriptDatabaseInitializer, ddlApplicationRunner, default.com.learningplatform.discussion.DiscussionServiceApplication.FeignClientSpecification, defaultServletHandlerMapping, defaultValidator, defaultViewResolver, defaultsBindHandlerAdvisor, discussionController, discussionReplyMapper, discussionServiceApplication, discussionServiceImpl, discussionTopicMapper, dispatcherServlet, dispatcherServletRegistration, environmentManager, error, errorAttributes, errorPageCustomizer, errorPageRegistrarBeanPostProcessor, feignChildContextInitializer, feignClientBeanFactoryInitializationCodeGenerator, feignContext, feignFeature, feignTargeter, fileWatcher, flashMapManager, formContentFilter, handlerExceptionResolver, handlerFunctionAdapter, healthController, hikariPoolDataSourceMetadataProvider, httpMessageConvertersRestClientCustomizer, httpRequestHandlerAdapter, inetIPv6Utils, inetUtils, inetUtilsProperties, jacksonObjectMapper, jacksonObjectMapperBuilder, jdbcClient, jdbcConnectionDetails, jdbcConnectionDetailsHikariBeanPostProcessor, jdbcTemplate, jsonComponentModule, jsonMixinModule, jsonMixinModuleEntries, lifecycleProcessor, loadBalancerClientsDefaultsMappingsProvider, localeCharsetMappingsCustomizer, localeResolver, loggingRebinder, mappingJackson2HttpMessageConverter, messageConverters, methodValidationPostProcessor, multipartConfigElement, multipartResolver, mvcContentNegotiationManager, mvcConversionService, mvcHandlerMappingIntrospector, mvcPathMatcher, mvcPatternParser, mvcResourceUrlProvider, mvcUriComponentsContributor, mvcUrlPathHelper, mvcValidator, mvcViewResolver, myBatisPlusConfig, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, nacosAutoServiceRegistration, nacosDiscoveryClient, nacosProperties, nacosRegistration, nacosServiceDiscovery, nacosServiceManager, nacosServiceRegistry, namedParameterJdbcTemplate, org.springframework.aop.config.internalAutoProxyCreator, org.springframework.boot.autoconfigure.AutoConfigurationPackages, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration$Hikari, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration, org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, org.springframework.cloud.commons.security.ResourceServerTokenRelayAutoConfiguration, org.springframework.cloud.commons.security.ResourceServerTokenRelayAutoConfiguration$ResourceServerTokenRelayRegistrationAutoConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.event.internalEventListenerFactory, org.springframework.context.event.internalEventListenerProcessor, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, parameterNamesModule, persistenceExceptionTranslationPostProcessor, platformTransactionManagerCustomizers, preserveErrorControllerTargetClassPostProcessor, propertySourcesPlaceholderConfigurer, refreshEventListener, refreshScope, refreshScopeLifecycle, replyLikeMapper, requestContextFilter, requestMappingHandlerAdapter, requestMappingHandlerMapping, resourceHandlerMapping, restClientBuilder, restClientBuilderConfigurer, restClientSsl, restTemplateBuilder, restTemplateBuilderConfigurer, routerFunctionMapping, server-org.springframework.boot.autoconfigure.web.ServerProperties, servletWebServerFactoryCustomizer, simpleAsyncTaskExecutorBuilder, simpleAsyncTaskSchedulerBuilder, simpleControllerHandlerAdapter, simpleDiscoveryClient, simpleDiscoveryProperties, sleuthPresentVerifier, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, spring.cloud.openfeign.client-org.springframework.cloud.openfeign.FeignClientProperties, spring.cloud.openfeign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, spring.cloud.openfeign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, springBootVersionVerifier, sqlSessionFactory, sqlSessionTemplate, sslBundleRegistry, sslPropertiesSslBundleRegistrar, standardJacksonObjectMapperBuilderCustomizer, stringHttpMessageConverter, taskExecutorBuilder, taskSchedulerBuilder, testDataController, themeResolver, threadPoolTaskExecutorBuilder, threadPoolTaskSchedulerBuilder, tomcatServletWebServerFactory, tomcatServletWebServerFactoryCustomizer, tomcatWebServerFactoryCustomizer, transactionAttributeSource, transactionExecutionListeners, transactionInterceptor, transactionManager, transactionTemplate, user-service.FeignClientSpecification, viewControllerHandlerMapping, viewNameTranslator, viewResolver, webServerFactoryCustomizerBeanPostProcessor, websocketServletWebServerCustomizer, welcomePageHandlerMapping, welcomePageNotAcceptableHandlerMapping]
[2m2025-08-01T18:02:03.451+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=1a34dac5-e725-3a71-b711-08d58ddd5936
[2m2025-08-01T18:02:03.558+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8085 (http)
[2m2025-08-01T18:02:03.561+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:02:03.561+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:02:03.580+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:02:03.580+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 363 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Initialization Sequence datacenterId:0 workerId:3
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
[2m2025-08-01T18:02:03.711+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.openfeign.FeignClientFactoryBean  [0;39m [2m:[0;39m Creating a FeignClientFactoryBean.
[2m2025-08-01T18:02:03.867+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.c.SpringBootVersionVerifier       [0;39m [2m:[0;39m Version found in Boot manifest [3.2.0]
[2m2025-08-01T18:02:03.867+08:00[0;39m [32mDEBUG[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.c.c.CompositeCompatibilityVerifier  [0;39m [2m:[0;39m All conditions are passing
[2m2025-08-01T18:02:03.881+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8085 (http) with context path ''
[2m2025-08-01T18:02:03.883+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:02:03.883+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:02:03.883+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:02:03.883+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:02:03.889+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:02:03.889+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:02:03.889+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:02:03.890+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:02:03.890+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:02:03.906+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:02:03.909+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of cfe06794-cf37-401d-a01f-7026ebf73d1a
[2m2025-08-01T18:02:03.914+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:02:03.914+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:02:03.914+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:02:03.914+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:02:03.923+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:02:04.077+08:00[0;39m [31mERROR[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754042524046_127.0.0.1_59659]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:02:04.177+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Success to connect to server [localhost:8848] on start up, connectionId = 1754042524046_127.0.0.1_59659
[2m2025-08-01T18:02:04.177+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Notify connected event to listeners.
[2m2025-08-01T18:02:04.177+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:02:04.177+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:02:04.178+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [cfe06794-cf37-401d-a01f-7026ebf73d1a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00000ff8016dd930
[2m2025-08-01T18:02:04.178+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service discussion-service with instance Instance{instanceId='null', ip='*************', port=8085, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:02:04.187+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP discussion-service *************:8085 register finished
[2m2025-08-01T18:02:04.193+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [           main][0;39m [2m[0;39m[36mc.l.d.DiscussionServiceApplication      [0;39m [2m:[0;39m Started DiscussionServiceApplication in 1.146 seconds (process running for 1.287)
[2m2025-08-01T18:02:38.087+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T18:02:38.088+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T18:02:38.089+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
[2m2025-08-01T18:02:43.823+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 获取课程讨论主题列表，课程ID: 1, 页码: 1, 大小: 1, 排序: latest
[2m2025-08-01T18:02:43.823+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 获取课程讨论主题列表，课程ID: 1, 页码: 1, 大小: 1, 排序: latest
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c38c2c] was not registered for synchronization because synchronization is not active
[2m2025-08-01T18:02:43.830+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Starting...
[2m2025-08-01T18:02:43.884+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4cc6087b
[2m2025-08-01T18:02:43.885+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Start completed.
JDBC Connection [HikariProxyConnection@1178884774 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: SELECT * FROM discussion_topics WHERE course_id = ? AND status = 'ACTIVE' ORDER BY is_pinned DESC, created_at DESC
==> Parameters: 1(Long)
<==    Columns: id, course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at
<==        Row: 6, 1, 1, Spring Boot学习讨论, <<BLOB>>, 1, 1, 0, ACTIVE, 2025-08-01 17:53:39, 2025-08-01 17:53:39
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c38c2c]
[2m2025-08-01T18:02:51.488+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-4][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 创建讨论主题请求，用户ID: 1, 请求: com.learningplatform.discussion.dto.DiscussionTopicCreateRequest@f5c2502
[2m2025-08-01T18:02:51.491+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-4][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 创建讨论主题，用户ID: 1, 课程ID: 1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1cb533eb]
JDBC Connection [HikariProxyConnection@1413208445 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will be managed by Spring
==>  Preparing: INSERT INTO discussion_topics ( course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1(Long), 1(Long), 真实用户信息测试(String), 测试真实的用户服务调用(String), 0(Integer), 0(Integer), false(Boolean), ACTIVE(String), 2025-08-01T18:02:51.495689(LocalDateTime), 2025-08-01T18:02:51.495693(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1cb533eb]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1cb533eb]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1cb533eb]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1cb533eb]
[2m2025-08-01T18:02:57.788+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-6][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 创建讨论回复请求，用户ID: 1, 请求: com.learningplatform.discussion.dto.DiscussionReplyCreateRequest@1275f77
[2m2025-08-01T18:02:57.789+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-6][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 创建讨论回复，用户ID: 1, 主题ID: 8
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
JDBC Connection [HikariProxyConnection@1975159123 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will be managed by Spring
==>  Preparing: SELECT id,course_id,user_id,title,content,reply_count,view_count,is_pinned,status,created_at,updated_at FROM discussion_topics WHERE id=?
==> Parameters: 8(Long)
<==    Columns: id, course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at
<==        Row: 8, 1, 1, 真实用户信息测试, <<BLOB>>, 0, 0, 0, ACTIVE, 2025-08-01 18:02:51, 2025-08-01 18:02:51
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407] from current transaction
==>  Preparing: INSERT INTO discussion_replies ( topic_id, user_id, content, like_count, status, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 8(Long), 1(Long), 这是一个真实用户信息的回复测试(String), 0(Integer), ACTIVE(String), 2025-08-01T18:02:57.790946(LocalDateTime), 2025-08-01T18:02:57.790950(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407] from current transaction
==>  Preparing: UPDATE discussion_topics SET reply_count = reply_count + 1 WHERE id = ?
==> Parameters: 8(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bdf3407]
[2m2025-08-01T18:03:39.989+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 创建讨论主题请求，用户ID: 1, 请求: com.learningplatform.discussion.dto.DiscussionTopicCreateRequest@290b1104
[2m2025-08-01T18:03:39.990+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 创建讨论主题，用户ID: 1, 课程ID: 1
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35e941b]
JDBC Connection [HikariProxyConnection@485931506 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will be managed by Spring
==>  Preparing: INSERT INTO discussion_topics ( course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 1(Long), 1(Long), 真实用户信息集成测试(String), 验证讨论服务与用户服务的真实集成(String), 0(Integer), 0(Integer), false(Boolean), ACTIVE(String), 2025-08-01T18:03:39.990486(LocalDateTime), 2025-08-01T18:03:39.990489(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35e941b]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35e941b]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35e941b]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35e941b]
[2m2025-08-01T18:03:40.012+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-4][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 创建讨论回复请求，用户ID: 1, 请求: com.learningplatform.discussion.dto.DiscussionReplyCreateRequest@31e70358
[2m2025-08-01T18:03:40.013+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-4][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 创建讨论回复，用户ID: 1, 主题ID: 9
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
JDBC Connection [HikariProxyConnection@901444352 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will be managed by Spring
==>  Preparing: SELECT id,course_id,user_id,title,content,reply_count,view_count,is_pinned,status,created_at,updated_at FROM discussion_topics WHERE id=?
==> Parameters: 9(Long)
<==    Columns: id, course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at
<==        Row: 9, 1, 1, 真实用户信息集成测试, <<BLOB>>, 0, 0, 0, ACTIVE, 2025-08-01 18:03:40, 2025-08-01 18:03:40
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91] from current transaction
==>  Preparing: INSERT INTO discussion_replies ( topic_id, user_id, content, like_count, status, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 9(Long), 1(Long), 这是使用真实用户信息的回复(String), 0(Integer), ACTIVE(String), 2025-08-01T18:03:40.014488(LocalDateTime), 2025-08-01T18:03:40.014492(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91] from current transaction
==>  Preparing: UPDATE discussion_topics SET reply_count = reply_count + 1 WHERE id = ?
==> Parameters: 9(Long)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37036e91]
[2m2025-08-01T18:03:40.034+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-6][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 获取课程讨论主题列表，课程ID: 1, 页码: 1, 大小: 5, 排序: latest
[2m2025-08-01T18:03:40.034+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-6][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 获取课程讨论主题列表，课程ID: 1, 页码: 1, 大小: 5, 排序: latest
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ab48a55] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@121412101 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: SELECT * FROM discussion_topics WHERE course_id = ? AND status = 'ACTIVE' ORDER BY is_pinned DESC, created_at DESC
==> Parameters: 1(Long)
<==    Columns: id, course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at
<==        Row: 9, 1, 1, 真实用户信息集成测试, <<BLOB>>, 1, 0, 0, ACTIVE, 2025-08-01 18:03:40, 2025-08-01 18:03:40
<==        Row: 8, 1, 1, 真实用户信息测试, <<BLOB>>, 1, 0, 0, ACTIVE, 2025-08-01 18:02:51, 2025-08-01 18:02:57
<==        Row: 6, 1, 1, Spring Boot学习讨论, <<BLOB>>, 1, 1, 0, ACTIVE, 2025-08-01 17:53:39, 2025-08-01 17:53:39
<==      Total: 3
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ab48a55]
[2m2025-08-01T18:03:40.054+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-8][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 获取主题回复列表，主题ID: 9
[2m2025-08-01T18:03:40.054+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-8][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 获取主题回复列表，主题ID: 9
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c8195e8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@881795976 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: SELECT * FROM discussion_replies WHERE topic_id = ? AND status = 'ACTIVE' ORDER BY created_at ASC
==> Parameters: 9(Long)
<==    Columns: id, topic_id, user_id, parent_id, content, like_count, status, created_at, updated_at
<==        Row: 4, 9, 1, null, <<BLOB>>, 0, ACTIVE, 2025-08-01 18:03:40, 2025-08-01 18:03:40
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c8195e8]
[2m2025-08-01T18:03:40.068+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [io-8085-exec-10][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 获取主题所有回复（嵌套结构），主题ID: 9
[2m2025-08-01T18:03:40.068+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [io-8085-exec-10][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 获取主题所有回复（嵌套结构），主题ID: 9
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16224ed8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1933200071 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: SELECT * FROM discussion_replies WHERE topic_id = ? AND status = 'ACTIVE' ORDER BY CASE WHEN parent_id IS NULL THEN id ELSE parent_id END, CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END, created_at ASC
==> Parameters: 9(Long)
<==    Columns: id, topic_id, user_id, parent_id, content, like_count, status, created_at, updated_at
<==        Row: 4, 9, 1, null, <<BLOB>>, 0, ACTIVE, 2025-08-01 18:03:40, 2025-08-01 18:03:40
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16224ed8]
[2m2025-08-01T18:03:40.080+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.controller.DiscussionController   [0;39m [2m:[0;39m 获取讨论主题详情，主题ID: 9
[2m2025-08-01T18:03:40.081+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [nio-8085-exec-2][0;39m [2m[0;39m[36mc.l.d.s.impl.DiscussionServiceImpl      [0;39m [2m:[0;39m 获取讨论主题详情，主题ID: 9
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4316e5b3] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@877726264 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: SELECT id,course_id,user_id,title,content,reply_count,view_count,is_pinned,status,created_at,updated_at FROM discussion_topics WHERE id=?
==> Parameters: 9(Long)
<==    Columns: id, course_id, user_id, title, content, reply_count, view_count, is_pinned, status, created_at, updated_at
<==        Row: 9, 1, 1, 真实用户信息集成测试, <<BLOB>>, 1, 0, 0, ACTIVE, 2025-08-01 18:03:40, 2025-08-01 18:03:40
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4316e5b3]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d497648] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1770402607 wrapping com.mysql.cj.jdbc.ConnectionImpl@4cc6087b] will not be managed by Spring
==>  Preparing: UPDATE discussion_topics SET view_count = view_count + 1 WHERE id = ?
==> Parameters: 9(Long)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d497648]
[2m2025-08-01T18:45:22.004+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Start destroying common HttpClient
[2m2025-08-01T18:45:22.004+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Start destroying Publisher
[2m2025-08-01T18:45:22.005+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [       Thread-4][0;39m [2m[0;39m[36mc.a.nacos.common.notify.NotifyCenter    [0;39m [2m:[0;39m [NotifyCenter] Destruction of the end
[2m2025-08-01T18:45:22.005+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [       Thread-7][0;39m [2m[0;39m[36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [HttpClientBeanHolder] Destruction of the end
[2m2025-08-01T18:45:22.007+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registering from Nacos Server now...
[2m2025-08-01T18:45:22.007+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [DEREGISTER-SERVICE] public deregistering service discussion-service with instance: Instance{instanceId='null', ip='*************', port=8085, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m De-registration finished.
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:22.009+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[2m2025-08-01T18:45:22.009+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Start destroying NacosRestTemplate
[2m2025-08-01T18:45:22.009+08:00[0;39m [33m WARN[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [NamingHttpClientManager] Destruction of the end
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[2m2025-08-01T18:45:22.009+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown rpc client, set status to shutdown
[2m2025-08-01T18:45:22.010+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@45cd8607[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[2m2025-08-01T18:45:22.010+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m Close current connection 1754042524046_127.0.0.1_59659
[2m2025-08-01T18:45:22.010+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [-localhost-1057][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754042524046_127.0.0.1_59659]Ignore complete event,isRunning:false,isAbandon=false
[2m2025-08-01T18:45:22.011+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@349f0ca4[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1058]
[2m2025-08-01T18:45:22.011+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@6ba6557e[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 865]
[2m2025-08-01T18:45:22.011+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m [null] CredentialWatcher is stopped
[2m2025-08-01T18:45:22.011+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialService  [0;39m [2m:[0;39m [null] CredentialService is freed
[2m2025-08-01T18:45:22.011+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
[2m2025-08-01T18:45:22.012+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Shutdown initiated...
[2m2025-08-01T18:45:22.012+08:00[0;39m [32m INFO[0;39m [35m19638[0;39m [2m---[0;39m [2m[discussion-service] [ionShutdownHook][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Shutdown completed.
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  43:19 min
[INFO] Finished at: 2025-08-01T18:45:22+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project discussion-service: Process terminated with exit code: 143 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
