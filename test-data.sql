-- 插入测试课程数据
INSERT INTO courses (id, title, description, instructor_id, category_id, price, duration, status, created_at, updated_at) 
VALUES 
(1, 'Spring Boot 入门教程', '从零开始学习Spring Boot框架', 1, 1, 99.00, 120, 'PUBLISHED', NOW(), NOW()),
(2, 'Java 高级编程', '深入学习Java高级特性', 1, 1, 199.00, 180, 'PUBLISHED', NOW(), NOW());

-- 插入测试用户数据（如果不存在）
INSERT IGNORE INTO users (id, username, email, password, role, status, created_at, updated_at)
VALUES 
(1, 'testuser', '<EMAIL>', 'password123', 'STUDENT', 'ACTIVE', NOW(), NOW());