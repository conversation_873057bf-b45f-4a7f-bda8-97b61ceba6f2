[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------< com.learningplatform:user-service >------------------
[INFO] Building User Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ user-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ user-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ user-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ user-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ user-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ user-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:46:06.977+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:46:06.995+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:06.997+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Starting UserServiceApplication using Java 23.0.2 with PID 31751 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/user-service)
[2m2025-08-01T18:46:06.997+08:00[0;39m [32mDEBUG[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:46:06.998+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:46:07.238+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=43f6f7d7-1e47-3aac-b09c-758343b790b5
[2m2025-08-01T18:46:07.332+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8081 (http)
[2m2025-08-01T18:46:07.335+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:46:07.336+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:46:07.351+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:46:07.351+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 333 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Initialization Sequence datacenterId:0 workerId:5
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.5 
[2m2025-08-01T18:46:07.613+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8081 (http) with context path ''
[2m2025-08-01T18:46:07.615+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:07.615+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:46:07.615+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:46:07.615+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:46:07.624+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:46:07.624+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:46:07.625+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:46:07.627+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:46:07.627+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:46:07.648+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:46:07.651+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 88116106-7db8-4d00-9d9b-539675c15904
[2m2025-08-01T18:46:07.656+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:46:07.656+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:46:07.656+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:46:07.657+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:46:07.668+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:46:07.837+08:00[0;39m [31mERROR[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754045167803_127.0.0.1_54551]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:46:07.936+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Success to connect to server [localhost:8848] on start up, connectionId = 1754045167803_127.0.0.1_54551
[2m2025-08-01T18:46:07.937+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Notify connected event to listeners.
[2m2025-08-01T18:46:07.937+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:46:07.937+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:46:07.937+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [88116106-7db8-4d00-9d9b-539675c15904] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x0000038001696c10
[2m2025-08-01T18:46:07.938+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service user-service with instance Instance{instanceId='null', ip='*************', port=8081, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:46:07.952+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP user-service *************:8081 register finished
[2m2025-08-01T18:46:07.959+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [           main][0;39m [2m[0;39m[36mc.l.user.UserServiceApplication         [0;39m [2m:[0;39m Started UserServiceApplication in 1.104 seconds (process running for 1.22)
[2m2025-08-01T20:39:24.407+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T20:39:24.407+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T20:39:24.408+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@492a3adc] was not registered for synchronization because synchronization is not active
[2m2025-08-01T20:39:30.655+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Starting...
[2m2025-08-01T20:39:30.709+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@354cb0f6
[2m2025-08-01T20:39:30.709+08:00[0;39m [32m INFO[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-2][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Start completed.
JDBC Connection [HikariProxyConnection@1088744501 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testuser(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 1, testuser, <EMAIL>, $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfKq.jSixu, 系统管理员, null, null, ADMIN, ACTIVE, 2025-08-01 15:04:05, 2025-08-01 17:53:38
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@492a3adc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24463e95] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@713151597 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: frontendtest(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 2, frontendtest, <EMAIL>, $2a$10$zQT9GIKJwwQnR.4gW9acSe12Fo/N6CY4qdCDgjmpqzKp183LWWsJ2, 前端测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 18:29:32, 2025-08-01 18:29:32
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24463e95]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a521a04] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1114849983 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a521a04]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ea01aa8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1821808972 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT COUNT(*) > 0 FROM users WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: COUNT(*) > 0
<==        Row: 0
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ea01aa8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4295a31f] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@888626597 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: INSERT INTO users ( username, email, password, nickname, role, status ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: testlearning(String), <EMAIL>(String), $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm(String), 学习测试用户(String), STUDENT(String), ACTIVE(String)
<==    Updates: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4295a31f]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@416e9a8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@181471066 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@416e9a8]
[2m2025-08-01T20:40:44.903+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:40:51.762+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:40:57.166+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [io-8081-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:41:00.281+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:41:47.793+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:41:51.246+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:41:52.549+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:46:30.613+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-8][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:46:34.573+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [io-8081-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T20:46:35.697+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64d7a121] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1149379699 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64d7a121]
[2m2025-08-01T21:01:12.206+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T21:01:15.678+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
[2m2025-08-01T21:01:16.833+08:00[0;39m [33m WARN[0;39m [35m31751[0;39m [2m---[0;39m [2m[user-service] [nio-8081-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public com.learningplatform.common.response.Result<com.learningplatform.user.dto.UserLoginResponse> com.learningplatform.user.controller.AuthController.login(com.learningplatform.user.dto.UserLoginRequest): [Field error in object 'userLoginRequest' on field 'usernameOrEmail': rejected value [null]; codes [NotBlank.userLoginRequest.usernameOrEmail,NotBlank.usernameOrEmail,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [userLoginRequest.usernameOrEmail,usernameOrEmail]; arguments []; default message [usernameOrEmail]]; default message [用户名或邮箱不能为空]] ]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3108fc18] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@188144937 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3108fc18]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@fd60680] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1533162189 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@fd60680]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36b9b991] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2005738902 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36b9b991]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4288395d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2047561783 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4288395d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f138ce1] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@891411134 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7f138ce1]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da2637c] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@950067884 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da2637c]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53f555f3] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@495803998 wrapping com.mysql.cj.jdbc.ConnectionImpl@354cb0f6] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53f555f3]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ff36e82] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@20218124 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ff36e82]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@652c1842] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@209919299 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@652c1842]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f2af299] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1878277593 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f2af299]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51a93793] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1942315407 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51a93793]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@546cf7ba] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2017567310 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@546cf7ba]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f75028] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@605865060 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@66f75028]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@293bfeb4] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1346570351 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@293bfeb4]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e4cdb80] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@434309164 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e4cdb80]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b6f61d8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@931041244 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4b6f61d8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@477f6bf] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@477075267 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@477f6bf]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@746256c8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1851478602 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@746256c8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64db6966] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@220576171 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64db6966]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3334dc] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1727917818 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7b3334dc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35adb8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1314455361 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35adb8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c01dbbb] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1556193757 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c01dbbb]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@645d9146] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@917519261 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@645d9146]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a061f8b] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2053248393 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a061f8b]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31501c14] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2000272403 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31501c14]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14dbeae1] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1409442232 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14dbeae1]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5181eaf8] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1971839928 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5181eaf8]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c13f07a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1928517386 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c13f07a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d801c8d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@23903877 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d801c8d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a756405] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@40102402 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a756405]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2323cc5d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1840387174 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2323cc5d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@361f9cd7] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@361718217 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@361f9cd7]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67005861] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1878746369 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67005861]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@621b3787] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1566303604 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@621b3787]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@641f2b48] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@593853602 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@641f2b48]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a0a9b4b] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@687730201 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5a0a9b4b]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@73c0f461] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@285522945 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@73c0f461]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c897ba9] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1189805795 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c897ba9]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@418131fc] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1066287276 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@418131fc]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5506245d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@930848701 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5506245d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36a0c12d] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1334813532 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36a0c12d]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7562f23a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2106417628 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7562f23a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74e7db15] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2125555736 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74e7db15]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70725f07] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@985308410 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70725f07]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e69343a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@2039386881 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e69343a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b855aed] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1238264036 wrapping com.mysql.cj.jdbc.ConnectionImpl@50b05f2] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b855aed]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2acc3b9a] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@576471408 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a95263a] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2acc3b9a]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a51e447] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@103886255 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a95263a] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a51e447]
Creating a new SqlSession
SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53ddbe4f] was not registered for synchronization because synchronization is not active
JDBC Connection [HikariProxyConnection@1093800429 wrapping com.mysql.cj.jdbc.ConnectionImpl@5a95263a] will not be managed by Spring
==>  Preparing: SELECT * FROM users WHERE username = ?
==> Parameters: testlearning(String)
<==    Columns: id, username, email, password, nickname, avatar_url, phone, role, status, created_at, updated_at
<==        Row: 6, testlearning, <EMAIL>, $2a$10$DFVqbGTQNEm6wDVXn5Fjw.7cQHzlDRmdfiWBxXA.1R9VQ3eHZpmxm, 学习测试用户, null, null, STUDENT, ACTIVE, 2025-08-01 20:39:42, 2025-08-01 20:39:42
<==      Total: 1
Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53ddbe4f]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  02:56 h
[INFO] Finished at: 2025-08-01T21:42:32+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project user-service: Process terminated with exit code: 137 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
