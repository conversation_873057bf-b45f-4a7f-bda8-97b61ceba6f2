#!/bin/bash

echo "=== 讨论服务真实数据测试 ==="

# 1. 验证用户服务可用
echo "1. 验证用户服务可用:"
USER_RESPONSE=$(curl -s http://localhost:8081/api/user/basic/1)
echo $USER_RESPONSE | jq .
echo -e "\n"

# 2. 验证讨论服务可用
echo "2. 验证讨论服务可用:"
curl -s http://localhost:8085/health | jq .
echo -e "\n"

# 3. 初始化测试数据
echo "3. 初始化测试数据:"
curl -s -X POST http://localhost:8085/api/init/test-data | jq .
echo -e "\n"

# 4. 创建讨论主题（验证真实用户信息）
echo "4. 创建讨论主题（验证真实用户信息）:"
TOPIC_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/topics \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 1" \
  -d '{
    "courseId": 1,
    "title": "真实用户信息集成测试",
    "content": "验证讨论服务与用户服务的真实集成"
  }')
echo $TOPIC_RESPONSE | jq .
TOPIC_ID=$(echo $TOPIC_RESPONSE | jq -r '.data.id // empty')
echo -e "\n"

if [ ! -z "$TOPIC_ID" ]; then
    # 5. 创建回复（验证真实用户信息）
    echo "5. 创建回复（验证真实用户信息）:"
    REPLY_RESPONSE=$(curl -s -X POST http://localhost:8085/api/discussion/replies \
      -H "Content-Type: application/json" \
      -H "X-User-Id: 1" \
      -d "{
        \"topicId\": $TOPIC_ID,
        \"content\": \"这是使用真实用户信息的回复\"
      }")
    echo $REPLY_RESPONSE | jq .
    REPLY_ID=$(echo $REPLY_RESPONSE | jq -r '.data.id // empty')
    echo -e "\n"

    # 6. 获取主题列表（验证用户信息显示）
    echo "6. 获取主题列表（验证用户信息显示）:"
    curl -s "http://localhost:8085/api/discussion/courses/1/topics?page=1&size=5" | jq '.data.records[] | {id, title, username, nickname, userRole}'
    echo -e "\n"

    # 7. 获取回复列表（验证用户信息显示）
    echo "7. 获取回复列表（验证用户信息显示）:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC_ID/replies?page=1&size=5" | jq '.data.records[] | {id, content, username, nickname, userRole}'
    echo -e "\n"

    # 8. 获取嵌套回复（验证用户信息显示）
    echo "8. 获取嵌套回复（验证用户信息显示）:"
    curl -s "http://localhost:8085/api/discussion/topics/$TOPIC_ID/replies/all" | jq '.data[] | {id, content, username, nickname, userRole, children}'
    echo -e "\n"

    # 9. 验证用户信息字段
    echo "9. 验证用户信息字段完整性:"
    TOPIC_DETAIL=$(curl -s "http://localhost:8085/api/discussion/topics/$TOPIC_ID")
    echo "主题用户信息:"
    echo $TOPIC_DETAIL | jq '.data | {username, nickname, avatarUrl, userRole}'
    echo -e "\n"

    # 10. 对比用户服务原始数据
    echo "10. 对比用户服务原始数据:"
    echo "用户服务原始数据:"
    curl -s http://localhost:8081/api/user/basic/1 | jq '.data | {username, nickname, avatarUrl, role}'
    echo "讨论服务显示数据:"
    echo $TOPIC_DETAIL | jq '.data | {username, nickname, avatarUrl, userRole}'
    echo -e "\n"

else
    echo "主题创建失败，跳过后续测试"
fi

echo "=== 真实数据测试完成 ==="