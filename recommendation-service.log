[INFO] Scanning for projects...
[INFO] 
[INFO] ------------< com.learningplatform:recommendation-service >-------------
[INFO] Building Recommendation Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ recommendation-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ recommendation-service ---
[INFO] Copying 1 resource from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ recommendation-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ recommendation-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/recommendation-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ recommendation-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ recommendation-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ recommendation-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:46:22.416+08:00[0;39m [33m WARN[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:46:22.433+08:00[0;39m [33m WARN[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:22.436+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.l.r.RecommendationServiceApplication  [0;39m [2m:[0;39m Starting RecommendationServiceApplication using Java 23.0.2 with PID 31920 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/recommendation-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/recommendation-service)
[2m2025-08-01T18:46:22.437+08:00[0;39m [32mDEBUG[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.l.r.RecommendationServiceApplication  [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:46:22.437+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.l.r.RecommendationServiceApplication  [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:46:22.654+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=40829c4a-4ab3-36bd-be1d-5e5be7c04418
[2m2025-08-01T18:46:22.753+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8084 (http)
[2m2025-08-01T18:46:22.756+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:46:22.757+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:46:22.777+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:46:22.778+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 321 ms
[2m2025-08-01T18:46:22.937+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8084 (http) with context path ''
[2m2025-08-01T18:46:22.939+08:00[0;39m [33m WARN[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:22.939+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:46:22.939+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:46:22.940+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:46:22.946+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:46:22.946+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:46:22.946+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:46:22.947+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:46:22.947+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:46:22.963+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:46:22.966+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 51d440ab-1b7d-4dfa-9eb3-64b50ea3b819
[2m2025-08-01T18:46:22.972+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:46:22.972+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:46:22.972+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:46:22.972+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:46:22.983+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:46:23.164+08:00[0;39m [31mERROR[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754045183130_127.0.0.1_54661]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:46:23.264+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Success to connect to server [localhost:8848] on start up, connectionId = 1754045183130_127.0.0.1_54661
[2m2025-08-01T18:46:23.264+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Notify connected event to listeners.
[2m2025-08-01T18:46:23.264+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:46:23.264+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:46:23.264+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [51d440ab-1b7d-4dfa-9eb3-64b50ea3b819] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00007f80015f3140
[2m2025-08-01T18:46:23.265+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service recommendation-service with instance Instance{instanceId='null', ip='*************', port=8084, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:46:23.274+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP recommendation-service *************:8084 register finished
[2m2025-08-01T18:46:23.280+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [           main][0;39m [2m[0;39m[36mc.l.r.RecommendationServiceApplication  [0;39m [2m:[0;39m Started RecommendationServiceApplication in 0.987 seconds (process running for 1.111)
[2m2025-08-01T21:33:17.539+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [nio-8084-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T21:33:17.540+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [nio-8084-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T21:33:17.541+08:00[0;39m [32m INFO[0;39m [35m31920[0;39m [2m---[0;39m [2m[recommendation-service] [nio-8084-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  03:01 h
[INFO] Finished at: 2025-08-01T21:47:34+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project recommendation-service: Process terminated with exit code: 137 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
