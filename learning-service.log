[INFO] Scanning for projects...
[INFO] 
[INFO] ---------------< com.learningplatform:learning-service >----------------
[INFO] Building Learning Service 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ learning-service >>>
[WARNING] The artifact mysql:mysql-connector-java:jar:8.0.33 has been relocated to com.mysql:mysql-connector-j:jar:8.0.33: MySQL Connector/J artifacts moved to reverse-DNS compliant Maven 2+ coordinates.
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ learning-service ---
[INFO] Copying 2 resources from src/main/resources to target/classes
[INFO] 
[INFO] --- compiler:3.13.0:compile (default-compile) @ learning-service ---
[INFO] Nothing to compile - all classes are up to date.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ learning-service ---
[INFO] skip non existing resourceDirectory /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/learning-service/src/test/resources
[INFO] 
[INFO] --- compiler:3.13.0:testCompile (default-testCompile) @ learning-service ---
[INFO] No sources to compile
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ learning-service <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ learning-service ---
[INFO] Attaching agents: []
[2m2025-08-01T18:46:16.829+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.2.0)[0;39m

[2m2025-08-01T18:46:16.848+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:16.851+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.l.learning.LearningServiceApplication [0;39m [2m:[0;39m Starting LearningServiceApplication using Java 23.0.2 with PID 31862 (/Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/learning-service/target/classes started by jstar in /Users/<USER>/Library/CloudStorage/OneDrive-XinEraLLC/桌面/接单/群内/2025-08-01-SpringCloud课程学习系统/Code/learning-service)
[2m2025-08-01T18:46:16.851+08:00[0;39m [32mDEBUG[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.l.learning.LearningServiceApplication [0;39m [2m:[0;39m Running with Spring Boot v3.2.0, Spring v6.1.1
[2m2025-08-01T18:46:16.852+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.l.learning.LearningServiceApplication [0;39m [2m:[0;39m No active profile set, falling back to 1 default profile: "default"
[2m2025-08-01T18:46:17.081+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-01T18:46:17.133+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 49 ms. Found 3 JPA repository interfaces.
[2m2025-08-01T18:46:17.192+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ae470429-a89d-3640-9bef-eef903ecee1d
[2m2025-08-01T18:46:17.243+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[2m2025-08-01T18:46:17.244+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration' of type [org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.244+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration' of type [org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.246+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.246+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.247+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x00007ff80142ddc8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.247+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.249+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.251+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.251+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.251+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerClientFactory' of type [org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.252+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'blockingLoadBalancerClient' of type [org.springframework.cloud.loadbalancer.blocking.client.BlockingLoadBalancerClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.255+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerServiceInstanceCookieTransformer' of type [org.springframework.cloud.loadbalancer.core.LoadBalancerServiceInstanceCookieTransformer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.255+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'xForwarderHeadersTransformer' of type [org.springframework.cloud.loadbalancer.blocking.XForwardedHeadersTransformer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.255+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.256+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerRequestFactory' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerRequestFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.256+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'loadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2m2025-08-01T18:46:17.325+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8083 (http)
[2m2025-08-01T18:46:17.329+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-01T18:46:17.329+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.16]
[2m2025-08-01T18:46:17.350+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-01T18:46:17.351+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 480 ms
[2m2025-08-01T18:46:17.380+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Starting...
[2m2025-08-01T18:46:17.452+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.zaxxer.hikari.pool.HikariPool       [0;39m [2m:[0;39m HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
[2m2025-08-01T18:46:17.453+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m HikariPool-1 - Start completed.
[2m2025-08-01T18:46:17.490+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-01T18:46:17.510+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.3.1.Final
[2m2025-08-01T18:46:17.521+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-01T18:46:17.601+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-01T18:46:17.889+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
Hibernate: 
    drop table if exists course_enrollments cascade 
Hibernate: 
    drop table if exists learning_progress cascade 
Hibernate: 
    drop table if exists quiz_records cascade 
Hibernate: 
    create table course_enrollments (
        progress_percentage numeric(38,2),
        completed_at timestamp(6),
        course_id bigint,
        enrolled_at timestamp(6),
        id bigint generated by default as identity,
        user_id bigint,
        primary key (id)
    )
Hibernate: 
    create table learning_progress (
        completed boolean,
        last_position_seconds integer,
        watch_duration_seconds integer,
        chapter_id bigint,
        completed_at timestamp(6),
        course_id bigint,
        id bigint generated by default as identity,
        user_id bigint,
        primary key (id)
    )
Hibernate: 
    create table quiz_records (
        score numeric(38,2),
        total_score numeric(38,2),
        chapter_id bigint,
        id bigint generated by default as identity,
        submitted_at timestamp(6),
        user_id bigint,
        primary key (id)
    )
[2m2025-08-01T18:46:17.898+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-01T18:46:18.015+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-01T18:46:18.189+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-08-01T18:46:18.200+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.c.openfeign.FeignClientFactoryBean  [0;39m [2m:[0;39m For 'course-service' URL not provided. Will try picking an instance via load-balancing.
[2m2025-08-01T18:46:18.363+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36miguration$LoadBalancerCaffeineWarnLogger[0;39m [2m:[0;39m Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
[2m2025-08-01T18:46:18.382+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8083 (http) with context path ''
[2m2025-08-01T18:46:18.384+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.nacos.client.logging.NacosLogging   [0;39m [2m:[0;39m Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
[2m2025-08-01T18:46:18.384+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ans.namespace attribute : null
[2m2025-08-01T18:46:18.385+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[2m2025-08-01T18:46:18.385+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m initializer namespace from namespace attribute :null
[2m2025-08-01T18:46:18.392+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [req-serv] nacos-server port:8848
[2m2025-08-01T18:46:18.392+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m [settings] [http-client] connect timeout:1000
[2m2025-08-01T18:46:18.392+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.alibaba.nacos.client.utils.ParamUtil  [0;39m [2m:[0;39m PER_TASK_CONFIG_SIZE: 3000.0
[2m2025-08-01T18:46:18.393+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[2m2025-08-01T18:46:18.393+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.n.p.a.s.c.ClientAuthPluginManager   [0;39m [2m:[0;39m [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[2m2025-08-01T18:46:18.410+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.a.r.identify.CredentialWatcher  [0;39m [2m:[0;39m null No credential found
[2m2025-08-01T18:46:18.413+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [RpcClientFactory] create a new rpc client of 261d28ff-7816-4ed6-878e-75105ead3d4e
[2m2025-08-01T18:46:18.419+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[2m2025-08-01T18:46:18.419+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[2m2025-08-01T18:46:18.419+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[2m2025-08-01T18:46:18.419+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
[2m2025-08-01T18:46:18.430+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"OPENSSL","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
[2m2025-08-01T18:46:18.612+08:00[0;39m [31mERROR[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [tor-localhost-8][0;39m [2m[0;39m[36mc.a.n.c.remote.client.grpc.GrpcClient   [0;39m [2m:[0;39m [1754045178577_127.0.0.1_54634]Error to process server push response: {"headers":{},"abilityTable":{"supportPersistentInstanceByGrpc":true},"module":"internal"}
[2m2025-08-01T18:46:18.712+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Success to connect to server [localhost:8848] on start up, connectionId = 1754045178577_127.0.0.1_54634
[2m2025-08-01T18:46:18.712+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Notify connected event to listeners.
[2m2025-08-01T18:46:18.712+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [t.remote.worker][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m Grpc connection connect
[2m2025-08-01T18:46:18.712+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[2m2025-08-01T18:46:18.713+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x00007ff801bbd108
[2m2025-08-01T18:46:18.713+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [REGISTER-SERVICE] public registering service learning-service with instance Instance{instanceId='null', ip='*************', port=8083, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[2m2025-08-01T18:46:18.722+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.a.c.n.registry.NacosServiceRegistry   [0;39m [2m:[0;39m nacos registry, DEFAULT_GROUP learning-service *************:8083 register finished
[2m2025-08-01T18:46:18.729+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [           main][0;39m [2m[0;39m[36mc.l.learning.LearningServiceApplication [0;39m [2m:[0;39m Started LearningServiceApplication in 2.021 seconds (process running for 2.149)
[2m2025-08-01T20:30:45.438+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-01T20:30:45.438+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-01T20:30:45.439+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 1 ms
[2m2025-08-01T20:32:03.013+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    insert 
    into
        course_enrollments
        (completed_at, course_id, enrolled_at, progress_percentage, user_id, id) 
    values
        (?, ?, ?, ?, ?, default)
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=? 
        and lp1_0.course_id=? 
        and lp1_0.chapter_id=?
Hibernate: 
    insert 
    into
        learning_progress
        (chapter_id, completed, completed_at, course_id, last_position_seconds, user_id, watch_duration_seconds, id) 
    values
        (?, ?, ?, ?, ?, ?, ?, default)
[2m2025-08-01T20:35:36.045+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m [SUBSCRIBE-SERVICE] service:course-service, group:DEFAULT_GROUP, clusters: 
[2m2025-08-01T20:35:36.052+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m init new ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T20:35:36.058+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
Hibernate: 
    select
        count(lp1_0.id) 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=? 
        and lp1_0.course_id=? 
        and lp1_0.completed=true
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
[2m2025-08-01T20:35:36.554+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-2310][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Receive server push request, request = NotifySubscriberRequest, requestId = 87
[2m2025-08-01T20:35:36.554+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-2310][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Ack server push request, request = NotifySubscriberRequest, requestId = 87
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=? 
        and lp1_0.course_id=? 
        and lp1_0.chapter_id=?
Hibernate: 
    update
        learning_progress 
    set
        chapter_id=?,
        completed=?,
        completed_at=?,
        course_id=?,
        last_position_seconds=?,
        user_id=?,
        watch_duration_seconds=? 
    where
        id=?
Hibernate: 
    select
        count(lp1_0.id) 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=? 
        and lp1_0.course_id=? 
        and lp1_0.completed=true
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    update
        course_enrollments 
    set
        completed_at=?,
        course_id=?,
        enrolled_at=?,
        progress_percentage=?,
        user_id=? 
    where
        id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    insert 
    into
        course_enrollments
        (completed_at, course_id, enrolled_at, progress_percentage, user_id, id) 
    values
        (?, ?, ?, ?, ?, default)
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=? 
        and ce1_0.course_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
Hibernate: 
    select
        ce1_0.id,
        ce1_0.completed_at,
        ce1_0.course_id,
        ce1_0.enrolled_at,
        ce1_0.progress_percentage,
        ce1_0.user_id 
    from
        course_enrollments ce1_0 
    where
        ce1_0.user_id=?
Hibernate: 
    select
        lp1_0.id,
        lp1_0.chapter_id,
        lp1_0.completed,
        lp1_0.completed_at,
        lp1_0.course_id,
        lp1_0.last_position_seconds,
        lp1_0.user_id,
        lp1_0.watch_duration_seconds 
    from
        learning_progress lp1_0 
    where
        lp1_0.user_id=?
[2m2025-08-01T21:26:18.359+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3260][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Receive server push request, request = NotifySubscriberRequest, requestId = 126
[2m2025-08-01T21:26:18.361+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3260][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:26:18.361+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3260][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@course-service -> []
[2m2025-08-01T21:26:18.363+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3260][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Ack server push request, request = NotifySubscriberRequest, requestId = 126
[2m2025-08-01T21:31:14.708+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3338][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Receive server push request, request = NotifySubscriberRequest, requestId = 127
[2m2025-08-01T21:31:14.711+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3338][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m new ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:31:14.712+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3338][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:31:14.714+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3338][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Ack server push request, request = NotifySubscriberRequest, requestId = 127
[2m2025-08-01T21:44:16.956+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:44:18.151+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:44:54.301+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:44:55.146+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:45:05.656+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:45:08.052+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:32.048+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-8][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:36.106+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-9][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:37.033+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [io-8083-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:37.807+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:38.190+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:38.463+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:38.700+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:48:38.909+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:49:18.047+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3648][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Receive server push request, request = NotifySubscriberRequest, requestId = 157
[2m2025-08-01T21:49:18.048+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3648][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m removed ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:49:18.048+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3648][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(0) service: DEFAULT_GROUP@@course-service -> []
[2m2025-08-01T21:49:18.053+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3648][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Ack server push request, request = NotifySubscriberRequest, requestId = 157
[2m2025-08-01T21:49:27.148+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3650][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Receive server push request, request = NotifySubscriberRequest, requestId = 158
[2m2025-08-01T21:49:27.149+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3650][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m new ips(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:49:27.149+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3650][0;39m [2m[0;39m[36mcom.alibaba.nacos.client.naming         [0;39m [2m:[0;39m current ips:(1) service: DEFAULT_GROUP@@course-service -> [{"instanceId":"*************#8082#DEFAULT#DEFAULT_GROUP@@course-service","ip":"*************","port":8082,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@course-service","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
[2m2025-08-01T21:49:27.150+08:00[0;39m [32m INFO[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [-localhost-3650][0;39m [2m[0;39m[36mcom.alibaba.nacos.common.remote.client  [0;39m [2m:[0;39m [261d28ff-7816-4ed6-878e-75105ead3d4e] Ack server push request, request = NotifySubscriberRequest, requestId = 158
[2m2025-08-01T21:50:27.002+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:50:32.552+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:50:33.281+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-8][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:50:35.123+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-9][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:50:38.198+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [io-8083-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:06.682+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:07.747+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:08.603+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:08.842+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:09.013+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:10.859+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.120+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.328+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-8][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.508+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-9][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.664+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [io-8083-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.799+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:11.925+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.050+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.150+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.282+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.400+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.708+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:12.824+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-8][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:13.915+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-9][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:14.069+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [io-8083-exec-10][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:15.339+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-1][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:15.455+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-2][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:23.417+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-3][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:23.541+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-4][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:23.691+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-5][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:23.831+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-6][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[2m2025-08-01T21:55:23.947+08:00[0;39m [33m WARN[0;39m [35m31862[0;39m [2m---[0;39m [2m[learning-service] [nio-8083-exec-7][0;39m [2m[0;39m[36m.w.s.m.s.DefaultHandlerExceptionResolver[0;39m [2m:[0;39m Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'userId' for method parameter type Long is not present]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  03:09 h
[INFO] Finished at: 2025-08-01T21:55:53+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.springframework.boot:spring-boot-maven-plugin:3.2.0:run (default-cli) on project learning-service: Process terminated with exit code: 137 -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoExecutionException
